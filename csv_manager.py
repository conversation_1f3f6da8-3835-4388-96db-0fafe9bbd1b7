"""
CSV Data Manager for Russian Proxy Collector
Handles CSV export/import, duplicate removal, and data persistence.
"""

import csv
import os
import shutil
import logging
from typing import List, Dict, Any, Set, Optional
from datetime import datetime
from pathlib import Path
from base_scraper import ProxyInfo
from config_manager import ConfigManager


class CSVManager:
    """Manages CSV data operations for proxy information."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the CSV manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.config = config_manager.get_output_config()
        self.logger = logging.getLogger(__name__)
        
        # CSV settings
        self.csv_file = Path(self.config.get('csv_file', 'russian_proxies.csv'))
        self.columns = self.config.get('csv_columns', [
            'ip', 'port', 'type', 'country', 'anonymity', 
            'response_time', 'last_tested', 'status', 'source', 'uptime', 'speed'
        ])
        self.backup_enabled = self.config.get('backup_enabled', True)
        self.max_backups = self.config.get('max_backups', 5)
        
        # Statistics
        self.stats = {
            'total_records': 0,
            'duplicates_removed': 0,
            'last_export': None,
            'last_import': None
        }
    
    def export_proxies(self, proxies: List[ProxyInfo], append: bool = False) -> None:
        """
        Export proxies to CSV file.
        
        Args:
            proxies: List of ProxyInfo objects to export
            append: Whether to append to existing file or overwrite
        """
        if not proxies:
            self.logger.warning("No proxies to export")
            return
        
        # Create backup if file exists and backup is enabled
        if self.backup_enabled and self.csv_file.exists() and not append:
            self._create_backup()
        
        # Determine write mode
        mode = 'a' if append and self.csv_file.exists() else 'w'
        write_header = mode == 'w' or not self.csv_file.exists()
        
        try:
            with open(self.csv_file, mode, newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.columns)
                
                if write_header:
                    writer.writeheader()
                
                # Convert proxies to dictionaries and write
                for proxy in proxies:
                    row = self._proxy_to_row(proxy)
                    writer.writerow(row)
            
            self.stats['total_records'] = len(proxies) if not append else self.stats['total_records'] + len(proxies)
            self.stats['last_export'] = datetime.now().isoformat()
            
            self.logger.info(f"Exported {len(proxies)} proxies to {self.csv_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to export proxies to CSV: {e}")
            raise
    
    def import_proxies(self) -> List[ProxyInfo]:
        """
        Import proxies from CSV file.
        
        Returns:
            List of ProxyInfo objects loaded from CSV
        """
        if not self.csv_file.exists():
            self.logger.info(f"CSV file {self.csv_file} does not exist")
            return []
        
        proxies = []
        
        try:
            with open(self.csv_file, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                # Validate CSV columns
                if not set(self.columns).issubset(set(reader.fieldnames or [])):
                    missing_cols = set(self.columns) - set(reader.fieldnames or [])
                    self.logger.warning(f"CSV file missing columns: {missing_cols}")
                
                for row_num, row in enumerate(reader, start=2):
                    try:
                        proxy = self._row_to_proxy(row)
                        proxies.append(proxy)
                    except Exception as e:
                        self.logger.warning(f"Skipping invalid row {row_num}: {e}")
                        continue
            
            self.stats['last_import'] = datetime.now().isoformat()
            self.logger.info(f"Imported {len(proxies)} proxies from {self.csv_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to import proxies from CSV: {e}")
            raise
        
        return proxies
    
    def remove_duplicates(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """
        Remove duplicate proxies based on IP and port.
        
        Args:
            proxies: List of ProxyInfo objects
            
        Returns:
            List of unique ProxyInfo objects
        """
        seen: Set[str] = set()
        unique_proxies = []
        duplicates_count = 0
        
        for proxy in proxies:
            proxy_key = f"{proxy.ip}:{proxy.port}"
            
            if proxy_key not in seen:
                seen.add(proxy_key)
                unique_proxies.append(proxy)
            else:
                duplicates_count += 1
        
        self.stats['duplicates_removed'] += duplicates_count
        
        if duplicates_count > 0:
            self.logger.info(f"Removed {duplicates_count} duplicate proxies")
        
        return unique_proxies
    
    def merge_proxy_data(self, existing_proxies: List[ProxyInfo], new_proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """
        Merge new proxy data with existing data, updating information for existing proxies.
        
        Args:
            existing_proxies: List of existing ProxyInfo objects
            new_proxies: List of new ProxyInfo objects
            
        Returns:
            List of merged ProxyInfo objects
        """
        # Create lookup dictionary for existing proxies
        existing_dict = {f"{p.ip}:{p.port}": p for p in existing_proxies}
        merged_proxies = []
        
        # Process new proxies
        for new_proxy in new_proxies:
            proxy_key = f"{new_proxy.ip}:{new_proxy.port}"
            
            if proxy_key in existing_dict:
                # Update existing proxy with new information
                existing_proxy = existing_dict[proxy_key]
                merged_proxy = self._merge_proxy_info(existing_proxy, new_proxy)
                merged_proxies.append(merged_proxy)
                # Remove from existing dict to track processed proxies
                del existing_dict[proxy_key]
            else:
                # Add new proxy
                merged_proxies.append(new_proxy)
        
        # Add remaining existing proxies that weren't updated
        merged_proxies.extend(existing_dict.values())
        
        return merged_proxies
    
    def _merge_proxy_info(self, existing: ProxyInfo, new: ProxyInfo) -> ProxyInfo:
        """
        Merge information from two ProxyInfo objects.
        
        Args:
            existing: Existing ProxyInfo object
            new: New ProxyInfo object
            
        Returns:
            Merged ProxyInfo object
        """
        # Keep the most recent test results
        if new.last_tested and (not existing.last_tested or new.last_tested > existing.last_tested):
            existing.response_time = new.response_time
            existing.last_tested = new.last_tested
            existing.status = new.status
        
        # Update other fields if new data is available
        if new.anonymity and new.anonymity != "Unknown":
            existing.anonymity = new.anonymity
        
        if new.uptime is not None:
            existing.uptime = new.uptime
        
        if new.speed is not None:
            existing.speed = new.speed
        
        # Always update source to show where it was last found
        if new.source:
            existing.source = new.source
        
        return existing
    
    def _proxy_to_row(self, proxy: ProxyInfo) -> Dict[str, Any]:
        """
        Convert ProxyInfo object to CSV row dictionary.
        
        Args:
            proxy: ProxyInfo object
            
        Returns:
            Dictionary representing CSV row
        """
        row = {}
        for column in self.columns:
            value = getattr(proxy, column, None)
            # Convert None values to empty strings for CSV
            row[column] = value if value is not None else ""
        return row
    
    def _row_to_proxy(self, row: Dict[str, str]) -> ProxyInfo:
        """
        Convert CSV row dictionary to ProxyInfo object.
        
        Args:
            row: Dictionary representing CSV row
            
        Returns:
            ProxyInfo object
        """
        # Required fields
        ip = row.get('ip', '').strip()
        port_str = row.get('port', '').strip()
        
        if not ip or not port_str:
            raise ValueError("Missing required fields: ip or port")
        
        try:
            port = int(port_str)
        except ValueError:
            raise ValueError(f"Invalid port number: {port_str}")
        
        # Optional fields with defaults
        proxy_type = row.get('type', 'HTTP').strip() or 'HTTP'
        country = row.get('country', 'RU').strip() or 'RU'
        anonymity = row.get('anonymity', 'Unknown').strip() or 'Unknown'
        status = row.get('status', 'Unknown').strip() or 'Unknown'
        source = row.get('source', '').strip()
        
        # Numeric fields
        response_time = None
        if row.get('response_time', '').strip():
            try:
                response_time = float(row['response_time'])
            except ValueError:
                pass
        
        uptime = None
        if row.get('uptime', '').strip():
            try:
                uptime = float(row['uptime'])
            except ValueError:
                pass
        
        speed = None
        if row.get('speed', '').strip():
            try:
                speed = int(row['speed'])
            except ValueError:
                pass
        
        last_tested = row.get('last_tested', '').strip() or None
        
        return ProxyInfo(
            ip=ip,
            port=port,
            type=proxy_type,
            country=country,
            anonymity=anonymity,
            response_time=response_time,
            last_tested=last_tested,
            status=status,
            source=source,
            uptime=uptime,
            speed=speed
        )
    
    def _create_backup(self) -> None:
        """Create a backup of the current CSV file."""
        if not self.csv_file.exists():
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{self.csv_file.stem}_backup_{timestamp}{self.csv_file.suffix}"
        backup_path = self.csv_file.parent / backup_name
        
        try:
            shutil.copy2(self.csv_file, backup_path)
            self.logger.info(f"Created backup: {backup_path}")
            
            # Clean up old backups
            self._cleanup_old_backups()
            
        except Exception as e:
            self.logger.warning(f"Failed to create backup: {e}")
    
    def _cleanup_old_backups(self) -> None:
        """Remove old backup files beyond the maximum limit."""
        backup_pattern = f"{self.csv_file.stem}_backup_*{self.csv_file.suffix}"
        backup_files = list(self.csv_file.parent.glob(backup_pattern))
        
        if len(backup_files) > self.max_backups:
            # Sort by modification time and remove oldest
            backup_files.sort(key=lambda x: x.stat().st_mtime)
            files_to_remove = backup_files[:-self.max_backups]
            
            for backup_file in files_to_remove:
                try:
                    backup_file.unlink()
                    self.logger.debug(f"Removed old backup: {backup_file}")
                except Exception as e:
                    self.logger.warning(f"Failed to remove old backup {backup_file}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get CSV manager statistics."""
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Reset CSV manager statistics."""
        self.stats = {
            'total_records': 0,
            'duplicates_removed': 0,
            'last_export': None,
            'last_import': None
        }
