#!/usr/bin/env python3
"""
Test script for Russian Proxy Collector
Validates the system components and basic functionality.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from config_manager import ConfigManager
        print("✓ ConfigManager imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ConfigManager: {e}")
        return False
    
    try:
        from base_scraper import BaseScraper, ProxyInfo
        print("✓ BaseScraper and ProxyInfo imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import BaseScraper/ProxyInfo: {e}")
        return False
    
    try:
        from proxy_validator import ProxyValidator
        print("✓ ProxyValidator imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ProxyValidator: {e}")
        return False
    
    try:
        from csv_manager import CSVManager
        print("✓ CSVManager imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import CSVManager: {e}")
        return False
    
    try:
        from scrapers import (
            ProxyNovaScraper, SpysOneScraper, FreeProxyListScraper,
            GeoNodeScraper, HideMyScraper
        )
        print("✓ All scrapers imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import scrapers: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading and validation."""
    print("\nTesting configuration...")
    
    try:
        from config_manager import ConfigManager
        
        # Check if config file exists
        if not Path("config.json").exists():
            print("✗ config.json file not found")
            return False
        
        # Load configuration
        config_manager = ConfigManager("config.json")
        print("✓ Configuration loaded successfully")
        
        # Test basic configuration access
        sources = config_manager.get_enabled_sources()
        print(f"✓ Found {len(sources)} enabled proxy sources")
        
        validation_config = config_manager.get_validation_config()
        test_urls = validation_config.get('test_urls', [])
        print(f"✓ Found {len(test_urls)} test URLs for validation")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_proxy_info():
    """Test ProxyInfo data class."""
    print("\nTesting ProxyInfo...")
    
    try:
        from base_scraper import ProxyInfo
        
        # Test valid proxy creation
        proxy = ProxyInfo(
            ip="***********",
            port=8080,
            type="HTTP",
            country="RU",
            anonymity="Elite",
            source="test"
        )
        print("✓ ProxyInfo object created successfully")
        
        # Test dictionary conversion
        proxy_dict = proxy.to_dict()
        assert 'ip' in proxy_dict
        assert 'port' in proxy_dict
        print("✓ ProxyInfo to_dict() works correctly")
        
        # Test string representation
        proxy_str = str(proxy)
        assert "***********:8080" in proxy_str
        print("✓ ProxyInfo string representation works")
        
        return True
        
    except Exception as e:
        print(f"✗ ProxyInfo test failed: {e}")
        return False

def test_csv_operations():
    """Test CSV operations."""
    print("\nTesting CSV operations...")
    
    try:
        from config_manager import ConfigManager
        from csv_manager import CSVManager
        from base_scraper import ProxyInfo
        
        config_manager = ConfigManager("config.json")
        csv_manager = CSVManager(config_manager)
        
        # Create test proxies
        test_proxies = [
            ProxyInfo(ip="***********", port=8080, source="test1"),
            ProxyInfo(ip="***********", port=3128, source="test2"),
            ProxyInfo(ip="***********", port=8080, source="test1")  # Duplicate
        ]
        
        # Test duplicate removal
        unique_proxies = csv_manager.remove_duplicates(test_proxies)
        assert len(unique_proxies) == 2
        print("✓ Duplicate removal works correctly")
        
        # Test CSV export (to temporary file)
        test_file = "test_proxies.csv"
        csv_manager.csv_file = Path(test_file)
        csv_manager.export_proxies(unique_proxies)
        print("✓ CSV export works correctly")
        
        # Test CSV import
        imported_proxies = csv_manager.import_proxies()
        assert len(imported_proxies) == 2
        print("✓ CSV import works correctly")
        
        # Clean up test file
        if Path(test_file).exists():
            Path(test_file).unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ CSV operations test failed: {e}")
        return False

def test_scraper_initialization():
    """Test scraper initialization."""
    print("\nTesting scraper initialization...")
    
    try:
        from config_manager import ConfigManager
        from scrapers import GeoNodeScraper
        
        config_manager = ConfigManager("config.json")
        geonode_config = config_manager.get_source_config("geonode")
        
        if geonode_config:
            scraper = GeoNodeScraper("geonode", geonode_config)
            print("✓ GeoNode scraper initialized successfully")
            
            # Test basic scraper properties
            assert scraper.name == "geonode"
            assert scraper.config == geonode_config
            print("✓ Scraper properties are correct")
            
            return True
        else:
            print("✗ GeoNode configuration not found")
            return False
        
    except Exception as e:
        print(f"✗ Scraper initialization test failed: {e}")
        return False

def test_main_application():
    """Test main application import."""
    print("\nTesting main application...")
    
    try:
        from russian_proxy_collector import RussianProxyCollector
        
        # Test initialization
        collector = RussianProxyCollector("config.json")
        print("✓ RussianProxyCollector initialized successfully")
        
        # Test scraper availability
        available_scrapers = list(collector.scrapers.keys())
        print(f"✓ Available scrapers: {', '.join(available_scrapers)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Main application test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Russian Proxy Collector - System Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_proxy_info,
        test_csv_operations,
        test_scraper_initialization,
        test_main_application
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! System is ready to use.")
        return 0
    else:
        print("✗ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
