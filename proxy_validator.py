"""
Proxy Validator for Russian Proxy Collector
Tests proxies for validity, speed, and anonymity level.
"""

import time
import json
import logging
import requests
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from base_scraper import ProxyInfo
from config_manager import ConfigManager


class ProxyValidator:
    """Validates proxy servers by testing connectivity and anonymity."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the proxy validator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.config = config_manager.get_validation_config()
        self.logger = logging.getLogger(__name__)
        
        # Validation settings
        self.test_urls = self.config.get('test_urls', [])
        self.timeout = self.config.get('timeout', 10)
        self.max_retries = self.config.get('max_retries', 2)
        self.concurrent_tests = self.config.get('concurrent_tests', 50)
        self.success_threshold = self.config.get('success_threshold', 0.5)
        self.anonymity_test_url = self.config.get('anonymity_test_url', 'http://httpbin.org/headers')
        
        # Statistics
        self.stats = {
            'total_tested': 0,
            'valid_proxies': 0,
            'invalid_proxies': 0,
            'timeout_errors': 0,
            'connection_errors': 0,
            'last_validation': None
        }
    
    def validate_proxy(self, proxy: ProxyInfo) -> ProxyInfo:
        """
        Validate a single proxy.
        
        Args:
            proxy: ProxyInfo object to validate
            
        Returns:
            Updated ProxyInfo object with validation results
        """
        self.logger.debug(f"Validating proxy: {proxy}")
        
        start_time = time.time()
        successful_tests = 0
        total_tests = 0
        response_times = []
        
        # Test proxy with multiple URLs
        for test_url in self.test_urls:
            for attempt in range(self.max_retries + 1):
                try:
                    result = self._test_proxy_url(proxy, test_url)
                    if result['success']:
                        successful_tests += 1
                        response_times.append(result['response_time'])
                    total_tests += 1
                    break  # Success, no need to retry
                    
                except Exception as e:
                    if attempt == self.max_retries:
                        self.logger.debug(f"Proxy {proxy} failed on {test_url}: {e}")
                        total_tests += 1
                    else:
                        time.sleep(0.5)  # Brief delay before retry
        
        # Calculate results
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        avg_response_time = sum(response_times) / len(response_times) if response_times else None
        
        # Update proxy info
        proxy.last_tested = datetime.now().isoformat()
        proxy.response_time = avg_response_time
        
        if success_rate >= self.success_threshold:
            proxy.status = "Valid"
            self.stats['valid_proxies'] += 1
            
            # Test anonymity level if proxy is valid
            try:
                anonymity = self._test_anonymity(proxy)
                if anonymity:
                    proxy.anonymity = anonymity
            except Exception as e:
                self.logger.debug(f"Anonymity test failed for {proxy}: {e}")
        else:
            proxy.status = "Invalid"
            self.stats['invalid_proxies'] += 1
        
        self.stats['total_tested'] += 1
        validation_time = time.time() - start_time
        self.logger.debug(f"Validated {proxy} in {validation_time:.2f}s: {proxy.status}")
        
        return proxy
    
    def _test_proxy_url(self, proxy: ProxyInfo, test_url: str) -> Dict[str, Any]:
        """
        Test proxy with a specific URL.
        
        Args:
            proxy: ProxyInfo object
            test_url: URL to test with
            
        Returns:
            Dictionary with test results
        """
        # Construct proxy URL based on proxy type
        proxy_type = proxy.type.lower()
        if proxy_type == 'socks4':
            proxy_url = f"socks4://{proxy.ip}:{proxy.port}"
        elif proxy_type == 'socks5':
            proxy_url = f"socks5://{proxy.ip}:{proxy.port}"
        elif proxy_type == 'https':
            proxy_url = f"https://{proxy.ip}:{proxy.port}"
        else:  # Default to HTTP for HTTP and unknown types
            proxy_url = f"http://{proxy.ip}:{proxy.port}"

        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        start_time = time.time()
        
        try:
            response = requests.get(
                test_url,
                proxies=proxies,
                timeout=self.timeout,
                verify=False,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'response_time': response_time,
                    'status_code': response.status_code
                }
            else:
                return {
                    'success': False,
                    'response_time': response_time,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.Timeout:
            self.stats['timeout_errors'] += 1
            return {
                'success': False,
                'response_time': time.time() - start_time,
                'error': 'timeout'
            }
        except requests.exceptions.ConnectionError:
            self.stats['connection_errors'] += 1
            return {
                'success': False,
                'response_time': time.time() - start_time,
                'error': 'connection_error'
            }
        except Exception as e:
            return {
                'success': False,
                'response_time': time.time() - start_time,
                'error': str(e)
            }
    
    def _test_anonymity(self, proxy: ProxyInfo) -> Optional[str]:
        """
        Test proxy anonymity level.
        
        Args:
            proxy: ProxyInfo object
            
        Returns:
            Anonymity level string or None if test fails
        """
        # Construct proxy URL based on proxy type
        proxy_type = proxy.type.lower()
        if proxy_type == 'socks4':
            proxy_url = f"socks4://{proxy.ip}:{proxy.port}"
        elif proxy_type == 'socks5':
            proxy_url = f"socks5://{proxy.ip}:{proxy.port}"
        elif proxy_type == 'https':
            proxy_url = f"https://{proxy.ip}:{proxy.port}"
        else:  # Default to HTTP for HTTP and unknown types
            proxy_url = f"http://{proxy.ip}:{proxy.port}"

        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        try:
            # Get headers through proxy
            response = requests.get(
                self.anonymity_test_url,
                proxies=proxies,
                timeout=self.timeout,
                verify=False,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            if response.status_code != 200:
                return None
            
            headers_data = response.json()
            headers = headers_data.get('headers', {})
            
            # Check for proxy-related headers
            proxy_headers = [
                'X-Forwarded-For',
                'X-Real-IP',
                'Via',
                'X-Proxy-ID',
                'HTTP_VIA',
                'HTTP_X_FORWARDED_FOR',
                'HTTP_FORWARDED_FOR',
                'HTTP_X_FORWARDED',
                'HTTP_FORWARDED',
                'HTTP_CLIENT_IP',
                'HTTP_FORWARDED_FOR_IP'
            ]
            
            # Check if any proxy headers are present
            has_proxy_headers = any(header in headers for header in proxy_headers)
            
            if not has_proxy_headers:
                return "Elite"  # No proxy headers detected
            else:
                # Check if real IP is exposed
                forwarded_for = headers.get('X-Forwarded-For', '')
                if forwarded_for and ',' in forwarded_for:
                    # Multiple IPs in X-Forwarded-For indicates transparent proxy
                    return "Transparent"
                else:
                    return "Anonymous"
                    
        except Exception as e:
            self.logger.debug(f"Anonymity test failed for {proxy}: {e}")
            return None
    
    def validate_proxies(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """
        Validate multiple proxies concurrently.
        
        Args:
            proxies: List of ProxyInfo objects to validate
            
        Returns:
            List of validated ProxyInfo objects
        """
        if not proxies:
            return []
        
        self.logger.info(f"Starting validation of {len(proxies)} proxies")
        self.stats['last_validation'] = datetime.now().isoformat()
        
        validated_proxies = []
        
        # Use ThreadPoolExecutor for concurrent validation
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.concurrent_tests) as executor:
            # Submit all validation tasks
            future_to_proxy = {
                executor.submit(self.validate_proxy, proxy): proxy 
                for proxy in proxies
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_proxy):
                try:
                    validated_proxy = future.result()
                    validated_proxies.append(validated_proxy)
                except Exception as e:
                    original_proxy = future_to_proxy[future]
                    self.logger.error(f"Validation failed for {original_proxy}: {e}")
                    # Add proxy with failed status
                    original_proxy.status = "Invalid"
                    original_proxy.last_tested = datetime.now().isoformat()
                    validated_proxies.append(original_proxy)
        
        valid_count = sum(1 for p in validated_proxies if p.status == "Valid")
        self.logger.info(f"Validation complete: {valid_count}/{len(proxies)} proxies are valid")
        
        return validated_proxies
    
    def get_valid_proxies(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """
        Get only valid proxies from a list.
        
        Args:
            proxies: List of ProxyInfo objects
            
        Returns:
            List of valid ProxyInfo objects
        """
        return [proxy for proxy in proxies if proxy.status == "Valid"]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get validation statistics."""
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Reset validation statistics."""
        self.stats = {
            'total_tested': 0,
            'valid_proxies': 0,
            'invalid_proxies': 0,
            'timeout_errors': 0,
            'connection_errors': 0,
            'last_validation': None
        }
