"""
Base classes and utilities for the Russian Proxy Server
Provides common functionality for both HTTP and SOCKS5 proxy protocols.
"""

import json
import logging
import socket
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import base64
import hashlib


@dataclass
class UpstreamProxy:
    """Represents an upstream proxy server."""
    ip: str
    port: int
    protocol: str  # HTTP, HTTPS, SOCKS4, SOCKS5
    anonymity: str = "Unknown"
    response_time: Optional[float] = None
    last_tested: Optional[str] = None
    status: str = "Unknown"
    source: str = ""
    failures: int = 0
    last_failure: Optional[float] = None
    success_count: int = 0
    total_requests: int = 0
    
    def __post_init__(self):
        """Validate proxy data after initialization."""
        if not (1 <= self.port <= 65535):
            raise ValueError(f"Invalid port number: {self.port}")
        
        self.protocol = self.protocol.upper()
        if self.protocol not in ["HTTP", "HTTPS", "SOCKS4", "SOCKS5"]:
            raise ValueError(f"Unsupported protocol: {self.protocol}")
    
    @property
    def is_healthy(self) -> bool:
        """Check if proxy is considered healthy."""
        return self.status == "Valid" and self.failures < 3
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 0.0
        return self.success_count / self.total_requests
    
    def record_success(self):
        """Record a successful request."""
        self.success_count += 1
        self.total_requests += 1
        self.failures = 0  # Reset failure count on success
    
    def record_failure(self):
        """Record a failed request."""
        self.failures += 1
        self.total_requests += 1
        self.last_failure = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'ip': self.ip,
            'port': self.port,
            'protocol': self.protocol,
            'anonymity': self.anonymity,
            'response_time': self.response_time,
            'status': self.status,
            'failures': self.failures,
            'success_rate': self.success_rate,
            'total_requests': self.total_requests
        }
    
    def __str__(self) -> str:
        """String representation."""
        return f"{self.ip}:{self.port} ({self.protocol})"


@dataclass
class ConnectionStats:
    """Statistics for a connection."""
    client_ip: str
    start_time: float
    protocol: str
    upstream_proxy: Optional[str] = None
    bytes_sent: int = 0
    bytes_received: int = 0
    end_time: Optional[float] = None
    error: Optional[str] = None
    
    @property
    def duration(self) -> float:
        """Get connection duration."""
        end = self.end_time or time.time()
        return end - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'client_ip': self.client_ip,
            'start_time': self.start_time,
            'duration': self.duration,
            'protocol': self.protocol,
            'upstream_proxy': self.upstream_proxy,
            'bytes_sent': self.bytes_sent,
            'bytes_received': self.bytes_received,
            'error': self.error
        }


class ProxyServerConfig:
    """Configuration manager for the proxy server."""
    
    def __init__(self, config_path: str = "proxy_server_config.json"):
        """Initialize configuration."""
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from JSON file."""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.logger.info(f"Configuration loaded from {self.config_path}")
            self._validate_config()
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def _validate_config(self) -> None:
        """Validate the loaded configuration."""
        required_sections = ['server', 'authentication', 'upstream_proxies', 'protocols']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
    
    def get(self, *keys: str, default: Any = None) -> Any:
        """Get nested configuration value."""
        current = self.config
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    
    def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration."""
        return self.config.get('server', {})
    
    def get_auth_config(self) -> Dict[str, Any]:
        """Get authentication configuration."""
        return self.config.get('authentication', {})
    
    def get_upstream_config(self) -> Dict[str, Any]:
        """Get upstream proxy configuration."""
        return self.config.get('upstream_proxies', {})
    
    def get_protocol_config(self, protocol: str) -> Dict[str, Any]:
        """Get protocol-specific configuration."""
        return self.config.get('protocols', {}).get(protocol, {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get('logging', {})
    
    def get_stats_config(self) -> Dict[str, Any]:
        """Get statistics configuration."""
        return self.config.get('statistics', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration."""
        return self.config.get('security', {})


class AuthenticationManager:
    """Handles authentication for proxy connections."""
    
    def __init__(self, config: ProxyServerConfig):
        """Initialize authentication manager."""
        self.config = config
        self.auth_config = config.get_auth_config()
        self.enabled = self.auth_config.get('enabled', True)
        self.username = self.auth_config.get('username', 'user')
        self.password = self.auth_config.get('password', 'pass')
        self.realm = self.auth_config.get('realm', 'Proxy Server')
        self.logger = logging.getLogger(__name__)
    
    def validate_credentials(self, username: str, password: str) -> bool:
        """Validate username and password."""
        if not self.enabled:
            return True
        
        return username == self.username and password == self.password
    
    def validate_http_auth(self, auth_header: str) -> bool:
        """Validate HTTP Basic authentication header."""
        if not self.enabled:
            return True
        
        if not auth_header or not auth_header.startswith('Basic '):
            return False
        
        try:
            # Decode base64 credentials
            encoded_creds = auth_header[6:]  # Remove 'Basic '
            decoded_creds = base64.b64decode(encoded_creds).decode('utf-8')
            username, password = decoded_creds.split(':', 1)
            
            return self.validate_credentials(username, password)
            
        except Exception as e:
            self.logger.debug(f"Failed to parse HTTP auth header: {e}")
            return False
    
    def get_http_auth_challenge(self) -> str:
        """Get HTTP 407 authentication challenge response."""
        return f'Basic realm="{self.realm}"'
    
    def validate_socks5_auth(self, username: str, password: str) -> bool:
        """Validate SOCKS5 username/password authentication."""
        return self.validate_credentials(username, password)


class BaseProxyHandler(ABC):
    """Abstract base class for proxy protocol handlers."""
    
    def __init__(self, config: ProxyServerConfig, auth_manager: AuthenticationManager):
        """Initialize base proxy handler."""
        self.config = config
        self.auth_manager = auth_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        self.active_connections: Dict[str, ConnectionStats] = {}
        self.connection_lock = threading.Lock()
    
    @abstractmethod
    def handle_connection(self, client_socket: socket.socket, client_address: Tuple[str, int]) -> None:
        """Handle incoming client connection."""
        pass
    
    def start_connection_stats(self, client_address: Tuple[str, int], protocol: str) -> str:
        """Start tracking connection statistics."""
        conn_id = f"{client_address[0]}:{client_address[1]}:{time.time()}"
        stats = ConnectionStats(
            client_ip=client_address[0],
            start_time=time.time(),
            protocol=protocol
        )
        
        with self.connection_lock:
            self.active_connections[conn_id] = stats
        
        return conn_id
    
    def end_connection_stats(self, conn_id: str, error: Optional[str] = None) -> None:
        """End connection statistics tracking."""
        with self.connection_lock:
            if conn_id in self.active_connections:
                stats = self.active_connections[conn_id]
                stats.end_time = time.time()
                stats.error = error
                # Move to completed connections (could be stored in a separate structure)
                del self.active_connections[conn_id]
    
    def update_connection_stats(self, conn_id: str, bytes_sent: int = 0, bytes_received: int = 0, 
                              upstream_proxy: Optional[str] = None) -> None:
        """Update connection statistics."""
        with self.connection_lock:
            if conn_id in self.active_connections:
                stats = self.active_connections[conn_id]
                stats.bytes_sent += bytes_sent
                stats.bytes_received += bytes_received
                if upstream_proxy:
                    stats.upstream_proxy = upstream_proxy
    
    def get_active_connections(self) -> List[Dict[str, Any]]:
        """Get list of active connections."""
        with self.connection_lock:
            return [stats.to_dict() for stats in self.active_connections.values()]
    
    def close_socket_safely(self, sock: socket.socket) -> None:
        """Safely close a socket."""
        try:
            sock.shutdown(socket.SHUT_RDWR)
        except:
            pass
        try:
            sock.close()
        except:
            pass


def setup_logging(config: ProxyServerConfig) -> None:
    """Setup logging configuration."""
    log_config = config.get_logging_config()
    
    # Determine log level
    level_str = log_config.get('level', 'INFO').upper()
    log_level = getattr(logging, level_str, logging.INFO)
    
    # Setup logging format
    log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Configure handlers
    handlers = []
    
    # Console handler
    if log_config.get('console_output', True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(console_handler)
    
    # File handler
    log_file = log_config.get('file', 'proxy_server.log')
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    # Reduce noise from other libraries
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
