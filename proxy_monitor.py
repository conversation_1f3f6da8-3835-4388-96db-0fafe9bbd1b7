#!/usr/bin/env python3
"""
Proxy Server Monitor
Real-time monitoring and statistics for the Russian Proxy Server.
"""

import json
import time
import threading
import logging
from typing import Dict, Any, List
from pathlib import Path
from datetime import datetime, timedelta
from proxy_server_base import ProxyServerConfig


class ProxyMonitor:
    """Real-time monitoring and statistics for the proxy server."""
    
    def __init__(self, config: ProxyServerConfig):
        """Initialize the proxy monitor."""
        self.config = config
        self.stats_config = config.get_stats_config()
        self.logger = logging.getLogger(__name__)
        
        # Statistics storage
        self.current_stats = {}
        self.history = []
        self.history_size = self.stats_config.get('history_size', 1000)
        
        # Export settings
        self.export_enabled = self.stats_config.get('enabled', True)
        self.export_interval = self.stats_config.get('export_interval', 300)
        self.export_file = Path(self.stats_config.get('export_file', 'proxy_stats.json'))
        
        # Threading
        self.lock = threading.RLock()
        self.export_thread: threading.Thread = None
        self.shutdown_event = threading.Event()
        
        # Start export thread
        if self.export_enabled and self.export_interval > 0:
            self.start_export_thread()
    
    def update_stats(self, stats: Dict[str, Any]) -> None:
        """Update current statistics."""
        with self.lock:
            # Add timestamp
            stats['timestamp'] = time.time()
            stats['datetime'] = datetime.now().isoformat()
            
            # Store current stats
            self.current_stats = stats.copy()
            
            # Add to history
            self.history.append(stats)
            
            # Trim history if needed
            if len(self.history) > self.history_size:
                self.history = self.history[-self.history_size:]
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        with self.lock:
            return self.current_stats.copy()
    
    def get_history(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get statistics history for the last N minutes."""
        cutoff_time = time.time() - (minutes * 60)
        
        with self.lock:
            return [
                stats for stats in self.history 
                if stats.get('timestamp', 0) >= cutoff_time
            ]
    
    def get_summary_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get summary statistics for the last N minutes."""
        history = self.get_history(minutes)
        
        if not history:
            return {}
        
        # Calculate averages and totals
        total_connections = sum(s.get('total_connections', 0) for s in history)
        active_connections = [s.get('active_connections', 0) for s in history]
        
        pool_stats = [s.get('pool_stats', {}) for s in history if s.get('pool_stats')]
        healthy_proxies = [p.get('healthy_proxies', 0) for p in pool_stats]
        
        summary = {
            'period_minutes': minutes,
            'total_connections': total_connections,
            'avg_active_connections': sum(active_connections) / len(active_connections) if active_connections else 0,
            'max_active_connections': max(active_connections) if active_connections else 0,
            'avg_healthy_proxies': sum(healthy_proxies) / len(healthy_proxies) if healthy_proxies else 0,
            'min_healthy_proxies': min(healthy_proxies) if healthy_proxies else 0,
            'data_points': len(history)
        }
        
        return summary
    
    def get_proxy_performance(self) -> Dict[str, Any]:
        """Get proxy performance statistics."""
        with self.lock:
            if not self.current_stats.get('pool_stats'):
                return {}
            
            pool_stats = self.current_stats['pool_stats']
            proxies = pool_stats.get('proxies', [])
            
            if not proxies:
                return {}
            
            # Calculate performance metrics
            total_requests = sum(p.get('total_requests', 0) for p in proxies)
            successful_requests = sum(p.get('success_count', 0) for p in proxies)
            
            # Top performing proxies
            top_proxies = sorted(
                [p for p in proxies if p.get('total_requests', 0) > 0],
                key=lambda x: x.get('success_rate', 0),
                reverse=True
            )[:10]
            
            # Fastest proxies
            fastest_proxies = sorted(
                [p for p in proxies if p.get('response_time')],
                key=lambda x: x.get('response_time', float('inf'))
            )[:10]
            
            performance = {
                'total_proxies': len(proxies),
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'overall_success_rate': successful_requests / total_requests if total_requests > 0 else 0,
                'top_performing_proxies': [
                    {
                        'proxy': f"{p['ip']}:{p['port']}",
                        'protocol': p.get('protocol', 'Unknown'),
                        'success_rate': p.get('success_rate', 0),
                        'total_requests': p.get('total_requests', 0)
                    }
                    for p in top_proxies
                ],
                'fastest_proxies': [
                    {
                        'proxy': f"{p['ip']}:{p['port']}",
                        'protocol': p.get('protocol', 'Unknown'),
                        'response_time': p.get('response_time', 0),
                        'success_rate': p.get('success_rate', 0)
                    }
                    for p in fastest_proxies
                ]
            }
            
            return performance
    
    def export_stats(self) -> None:
        """Export statistics to file."""
        try:
            export_data = {
                'export_time': datetime.now().isoformat(),
                'current_stats': self.get_current_stats(),
                'summary_1h': self.get_summary_stats(60),
                'summary_24h': self.get_summary_stats(1440),
                'proxy_performance': self.get_proxy_performance(),
                'recent_history': self.get_history(60)  # Last hour
            }
            
            with open(self.export_file, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            self.logger.debug(f"Statistics exported to {self.export_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to export statistics: {e}")
    
    def start_export_thread(self) -> None:
        """Start the statistics export thread."""
        self.export_thread = threading.Thread(
            target=self._export_worker,
            daemon=True,
            name="StatsExportWorker"
        )
        self.export_thread.start()
    
    def _export_worker(self) -> None:
        """Background worker for statistics export."""
        while not self.shutdown_event.is_set():
            try:
                self.export_stats()
            except Exception as e:
                self.logger.error(f"Export worker error: {e}")
            
            # Wait for next export
            self.shutdown_event.wait(self.export_interval)
    
    def print_status(self) -> None:
        """Print current server status to console."""
        stats = self.get_current_stats()
        
        if not stats:
            print("No statistics available")
            return
        
        print("\n" + "="*60)
        print("RUSSIAN PROXY SERVER - STATUS")
        print("="*60)
        
        # Server info
        uptime = stats.get('uptime', 0)
        uptime_str = str(timedelta(seconds=int(uptime)))
        print(f"Uptime: {uptime_str}")
        print(f"Start Time: {datetime.fromtimestamp(stats.get('start_time', 0)).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Connection stats
        print(f"\nConnections:")
        print(f"  Active: {stats.get('active_connections', 0)}")
        print(f"  Total HTTP: {stats.get('http_connections', 0)}")
        print(f"  Total SOCKS5: {stats.get('socks_connections', 0)}")
        print(f"  Total: {stats.get('total_connections', 0)}")
        
        # Proxy pool stats
        pool_stats = stats.get('pool_stats', {})
        if pool_stats:
            print(f"\nProxy Pool:")
            print(f"  Total Proxies: {pool_stats.get('total_proxies', 0)}")
            print(f"  Healthy: {pool_stats.get('healthy_proxies', 0)}")
            print(f"  Failed: {pool_stats.get('failed_proxies', 0)}")
            print(f"  Selection Mode: {pool_stats.get('selection_mode', 'Unknown')}")
            
            if pool_stats.get('last_health_check'):
                last_check = datetime.fromtimestamp(pool_stats['last_health_check'])
                print(f"  Last Health Check: {last_check.strftime('%H:%M:%S')}")
        
        # Performance summary
        performance = self.get_proxy_performance()
        if performance:
            print(f"\nPerformance:")
            print(f"  Total Requests: {performance.get('total_requests', 0)}")
            print(f"  Successful: {performance.get('successful_requests', 0)}")
            print(f"  Success Rate: {performance.get('overall_success_rate', 0):.1%}")
        
        print("="*60)
    
    def shutdown(self) -> None:
        """Shutdown the monitor."""
        self.logger.info("Shutting down proxy monitor")
        self.shutdown_event.set()
        
        if self.export_thread and self.export_thread.is_alive():
            self.export_thread.join(timeout=5)
        
        # Final export
        if self.export_enabled:
            self.export_stats()


def main():
    """Standalone monitoring script."""
    import argparse
    import signal
    
    parser = argparse.ArgumentParser(description="Proxy Server Monitor")
    parser.add_argument('--config', default='proxy_server_config.json', help='Config file path')
    parser.add_argument('--interval', type=int, default=10, help='Update interval in seconds')
    parser.add_argument('--export-only', action='store_true', help='Export stats and exit')
    
    args = parser.parse_args()
    
    if not Path(args.config).exists():
        print(f"Error: Configuration file '{args.config}' not found")
        return 1
    
    try:
        config = ProxyServerConfig(args.config)
        monitor = ProxyMonitor(config)
        
        if args.export_only:
            # Load existing stats if available
            if monitor.export_file.exists():
                with open(monitor.export_file, 'r') as f:
                    data = json.load(f)
                    if 'current_stats' in data:
                        monitor.update_stats(data['current_stats'])
            
            monitor.export_stats()
            print(f"Statistics exported to {monitor.export_file}")
            return 0
        
        # Interactive monitoring
        def signal_handler(signum, frame):
            monitor.shutdown()
            exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        print("Proxy Server Monitor - Press Ctrl+C to exit")
        
        while True:
            try:
                # Load current stats from export file if available
                if monitor.export_file.exists():
                    with open(monitor.export_file, 'r') as f:
                        data = json.load(f)
                        if 'current_stats' in data:
                            monitor.update_stats(data['current_stats'])
                
                monitor.print_status()
                time.sleep(args.interval)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Monitor error: {e}")
                time.sleep(args.interval)
        
        monitor.shutdown()
        return 0
        
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
