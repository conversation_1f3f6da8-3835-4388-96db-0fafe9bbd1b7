#!/usr/bin/env python3
"""
Russian Proxy Collector
A robust Python script for collecting and validating Russian proxies from multiple sources.

Usage:
    python russian_proxy_collector.py --collect --validate --output proxies.csv
    python russian_proxy_collector.py --validate-only --input proxies.csv
    python russian_proxy_collector.py --collect --sources proxynova,spys_one
"""

import argparse
import logging
import sys
import time
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Import our modules
from config_manager import ConfigManager
from base_scraper import ProxyInfo
from proxy_validator import ProxyValidator
from csv_manager import CSVManager
from scrapers import (
    ProxyNovaScraper,
    SpysOneScraper,
    FreeProxyListScraper,
    GeoNodeScraper,
    HideMyScraper
)


class RussianProxyCollector:
    """Main application class for the Russian Proxy Collector."""
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the proxy collector.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.validator = ProxyValidator(self.config_manager)
        self.csv_manager = CSVManager(self.config_manager)
        self.logger = logging.getLogger(__name__)
        
        # Initialize scrapers
        self.scrapers = self._initialize_scrapers()
        
        # Statistics
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_collected': 0,
            'total_validated': 0,
            'valid_proxies': 0,
            'sources_used': []
        }
    
    def _initialize_scrapers(self) -> Dict[str, Any]:
        """Initialize all available scrapers."""
        scrapers = {}
        scraper_classes = {
            'proxynova': ProxyNovaScraper,
            'spys_one': SpysOneScraper,
            'free_proxy_list': FreeProxyListScraper,
            'geonode': GeoNodeScraper,
            'hidemy_name': HideMyScraper
        }
        
        for source_name, source_config in self.config_manager.get_proxy_sources().items():
            if source_name in scraper_classes:
                try:
                    scraper_class = scraper_classes[source_name]
                    scraper = scraper_class(source_name, source_config)
                    scrapers[source_name] = scraper
                    self.logger.debug(f"Initialized scraper: {source_name}")
                except Exception as e:
                    self.logger.error(f"Failed to initialize scraper {source_name}: {e}")
            else:
                self.logger.warning(f"No scraper class found for source: {source_name}")
        
        return scrapers
    
    def collect_proxies(self, sources: List[str] = None) -> List[ProxyInfo]:
        """
        Collect proxies from specified sources.
        
        Args:
            sources: List of source names to use, or None for all enabled sources
            
        Returns:
            List of collected ProxyInfo objects
        """
        self.logger.info("Starting proxy collection")
        all_proxies = []
        
        # Determine which sources to use
        if sources:
            enabled_sources = {name: config for name, config in self.config_manager.get_enabled_sources().items() if name in sources}
        else:
            enabled_sources = self.config_manager.get_enabled_sources()
        
        if not enabled_sources:
            self.logger.warning("No enabled sources found")
            return all_proxies
        
        self.stats['sources_used'] = list(enabled_sources.keys())
        
        # Collect from each source
        for source_name in enabled_sources:
            if source_name not in self.scrapers:
                self.logger.warning(f"Scraper not available for source: {source_name}")
                continue
            
            scraper = self.scrapers[source_name]
            
            try:
                self.logger.info(f"Collecting proxies from {source_name}")
                source_proxies = scraper.scrape_proxies()
                
                if source_proxies:
                    all_proxies.extend(source_proxies)
                    self.logger.info(f"Collected {len(source_proxies)} proxies from {source_name}")
                else:
                    self.logger.warning(f"No proxies collected from {source_name}")
                
            except Exception as e:
                self.logger.error(f"Failed to collect from {source_name}: {e}")
                continue
        
        # Remove duplicates
        unique_proxies = self.csv_manager.remove_duplicates(all_proxies)
        self.stats['total_collected'] = len(unique_proxies)
        
        self.logger.info(f"Collected {len(unique_proxies)} unique proxies from {len(enabled_sources)} sources")
        return unique_proxies
    
    def validate_proxies(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """
        Validate a list of proxies.
        
        Args:
            proxies: List of ProxyInfo objects to validate
            
        Returns:
            List of validated ProxyInfo objects
        """
        if not proxies:
            self.logger.warning("No proxies to validate")
            return []
        
        self.logger.info(f"Starting validation of {len(proxies)} proxies")
        validated_proxies = self.validator.validate_proxies(proxies)
        
        valid_count = len(self.validator.get_valid_proxies(validated_proxies))
        self.stats['total_validated'] = len(validated_proxies)
        self.stats['valid_proxies'] = valid_count
        
        self.logger.info(f"Validation complete: {valid_count}/{len(validated_proxies)} proxies are valid")
        return validated_proxies
    
    def save_proxies(self, proxies: List[ProxyInfo], output_file: str = None, append: bool = False) -> None:
        """
        Save proxies to CSV file.
        
        Args:
            proxies: List of ProxyInfo objects to save
            output_file: Output file path (uses config default if None)
            append: Whether to append to existing file
        """
        if not proxies:
            self.logger.warning("No proxies to save")
            return
        
        if output_file:
            # Temporarily update config for custom output file
            original_file = self.config_manager.get_csv_file_path()
            self.config_manager.config['output']['csv_file'] = output_file
            self.csv_manager.csv_file = Path(output_file)
        
        try:
            self.csv_manager.export_proxies(proxies, append=append)
            self.logger.info(f"Saved {len(proxies)} proxies to {self.csv_manager.csv_file}")
        finally:
            if output_file:
                # Restore original config
                self.config_manager.config['output']['csv_file'] = original_file
                self.csv_manager.csv_file = Path(original_file)
    
    def load_proxies(self, input_file: str = None) -> List[ProxyInfo]:
        """
        Load proxies from CSV file.
        
        Args:
            input_file: Input file path (uses config default if None)
            
        Returns:
            List of loaded ProxyInfo objects
        """
        if input_file:
            # Temporarily update config for custom input file
            original_file = self.config_manager.get_csv_file_path()
            self.csv_manager.csv_file = Path(input_file)
        
        try:
            proxies = self.csv_manager.import_proxies()
            self.logger.info(f"Loaded {len(proxies)} proxies from {self.csv_manager.csv_file}")
            return proxies
        finally:
            if input_file:
                # Restore original config
                self.csv_manager.csv_file = Path(original_file)
    
    def print_statistics(self) -> None:
        """Print collection and validation statistics."""
        print("\n" + "="*60)
        print("RUSSIAN PROXY COLLECTOR - STATISTICS")
        print("="*60)
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            print(f"Execution Time: {duration:.2f} seconds")
        
        print(f"Sources Used: {', '.join(self.stats['sources_used']) if self.stats['sources_used'] else 'None'}")
        print(f"Total Collected: {self.stats['total_collected']}")
        print(f"Total Validated: {self.stats['total_validated']}")
        print(f"Valid Proxies: {self.stats['valid_proxies']}")
        
        if self.stats['total_validated'] > 0:
            success_rate = (self.stats['valid_proxies'] / self.stats['total_validated']) * 100
            print(f"Success Rate: {success_rate:.1f}%")
        
        # Print scraper statistics
        print("\nScraper Statistics:")
        for name, scraper in self.scrapers.items():
            stats = scraper.get_stats()
            print(f"  {name}: {stats['proxies_found']} found, {stats['requests_made']} requests, {stats['errors']} errors")
        
        # Print validator statistics
        validator_stats = self.validator.get_stats()
        print(f"\nValidator Statistics:")
        print(f"  Timeout Errors: {validator_stats['timeout_errors']}")
        print(f"  Connection Errors: {validator_stats['connection_errors']}")
        
        # Print CSV manager statistics
        csv_stats = self.csv_manager.get_stats()
        print(f"\nCSV Manager Statistics:")
        print(f"  Duplicates Removed: {csv_stats['duplicates_removed']}")
        
        print("="*60)
    
    def run(self, args) -> int:
        """
        Run the proxy collector with given arguments.
        
        Args:
            args: Parsed command line arguments
            
        Returns:
            Exit code (0 for success, 1 for error)
        """
        self.stats['start_time'] = time.time()
        
        try:
            if args.collect or args.collect_only:
                # Collect proxies
                sources = args.sources.split(',') if args.sources else None
                proxies = self.collect_proxies(sources)
                
                if not proxies:
                    self.logger.error("No proxies collected")
                    return 1
                
                if args.validate and not args.collect_only:
                    # Validate collected proxies
                    proxies = self.validate_proxies(proxies)
                
                # Save proxies
                self.save_proxies(proxies, args.output, args.append)
                
            elif args.validate_only:
                # Load and validate existing proxies
                proxies = self.load_proxies(args.input)
                
                if not proxies:
                    self.logger.error("No proxies loaded for validation")
                    return 1
                
                proxies = self.validate_proxies(proxies)
                self.save_proxies(proxies, args.output, args.append)
            
            else:
                self.logger.error("No action specified. Use --collect, --validate-only, or --collect-only")
                return 1
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            return 1
        
        finally:
            self.stats['end_time'] = time.time()
            if args.verbose:
                self.print_statistics()


def setup_logging(config_manager: ConfigManager, verbose: bool = False) -> None:
    """
    Setup logging configuration.

    Args:
        config_manager: Configuration manager instance
        verbose: Enable verbose logging
    """
    log_config = config_manager.get_logging_config()

    # Determine log level
    if verbose:
        log_level = logging.DEBUG
    else:
        level_str = log_config.get('level', 'INFO').upper()
        log_level = getattr(logging, level_str, logging.INFO)

    # Setup logging format
    log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_config.get('file', 'proxy_collector.log'))
        ]
    )

    # Reduce noise from requests library
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def main() -> int:
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Russian Proxy Collector - Collect and validate Russian proxies from multiple sources",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Collect and validate proxies from all sources
  python russian_proxy_collector.py --collect --validate

  # Collect from specific sources only
  python russian_proxy_collector.py --collect --sources proxynova,spys_one

  # Validate existing proxies from CSV file
  python russian_proxy_collector.py --validate-only --input existing_proxies.csv

  # Collect without validation
  python russian_proxy_collector.py --collect-only --output raw_proxies.csv

  # Append to existing CSV file
  python russian_proxy_collector.py --collect --validate --append
        """
    )

    # Action arguments
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument(
        '--collect',
        action='store_true',
        help='Collect proxies from sources (can be combined with --validate)'
    )
    action_group.add_argument(
        '--collect-only',
        action='store_true',
        help='Collect proxies without validation'
    )
    action_group.add_argument(
        '--validate-only',
        action='store_true',
        help='Validate existing proxies from input file'
    )

    # Optional arguments
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Validate collected proxies (used with --collect)'
    )
    parser.add_argument(
        '--sources',
        type=str,
        help='Comma-separated list of sources to use (e.g., proxynova,spys_one)'
    )
    parser.add_argument(
        '--output',
        type=str,
        help='Output CSV file path (default from config)'
    )
    parser.add_argument(
        '--input',
        type=str,
        help='Input CSV file path for validation (default from config)'
    )
    parser.add_argument(
        '--config',
        type=str,
        default='config.json',
        help='Configuration file path (default: config.json)'
    )
    parser.add_argument(
        '--append',
        action='store_true',
        help='Append to existing CSV file instead of overwriting'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging and show statistics'
    )

    args = parser.parse_args()

    # Check if config file exists
    if not Path(args.config).exists():
        print(f"Error: Configuration file '{args.config}' not found")
        return 1

    try:
        # Initialize configuration and logging
        config_manager = ConfigManager(args.config)
        setup_logging(config_manager, args.verbose)

        # Create and run the collector
        collector = RussianProxyCollector(args.config)
        return collector.run(args)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
