"""
Specific Proxy Source Scrapers for Russian Proxy Collector
Individual scraper implementations for different proxy sources.
"""

import re
from typing import List, Optional
from datetime import datetime
try:
    from bs4 import BeautifulSoup
except ImportError:
    print("Error: BeautifulSoup4 is required. Install with: pip install beautifulsoup4")
    raise
from base_scraper import BaseScraper, ProxyInfo


class ProxyNovaScraper(BaseScraper):
    """Scraper for ProxyNova.com Russian proxy list."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from ProxyNova."""
        self.logger.info("Scraping proxies from ProxyNova")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the proxy table
            proxy_table = soup.find('table')
            if not proxy_table:
                self.logger.warning("Could not find proxy table on ProxyNova")
                return proxies
            
            rows = proxy_table.find_all('tr')[1:]  # Skip header row
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 7:
                        continue
                    
                    # Extract proxy information
                    ip_cell = cells[0]
                    port_cell = cells[1]
                    
                    # ProxyNova uses JavaScript to obfuscate IPs, try to extract
                    ip = self._extract_ip_from_cell(ip_cell)
                    port_text = self._clean_text(port_cell.get_text())
                    
                    if not ip or not port_text.isdigit():
                        continue
                    
                    port = int(port_text)
                    
                    # Extract additional information
                    country = "RU"  # We're specifically scraping Russian proxies
                    anonymity = self._extract_anonymity(cells[6] if len(cells) > 6 else None)
                    
                    # Extract uptime if available
                    uptime = None
                    if len(cells) > 4:
                        uptime_text = self._clean_text(cells[4].get_text())
                        uptime_match = re.search(r'(\d+)%', uptime_text)
                        if uptime_match:
                            uptime = float(uptime_match.group(1))
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type="HTTP",
                        country=country,
                        anonymity=anonymity,
                        source=self.name,
                        uptime=uptime
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing ProxyNova row: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} proxies from ProxyNova")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape ProxyNova: {e}")
            raise
        
        return proxies
    
    def _extract_ip_from_cell(self, cell) -> Optional[str]:
        """Extract IP address from ProxyNova cell (handles obfuscation)."""
        # Try to get plain text first
        text = self._clean_text(cell.get_text())
        
        # Check if it looks like an IP address
        ip_pattern = r'\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b'
        match = re.search(ip_pattern, text)
        if match:
            return match.group(1)
        
        # If no plain IP found, try to extract from JavaScript/obfuscated content
        # ProxyNova sometimes uses document.write or other JS methods
        scripts = cell.find_all('script')
        for script in scripts:
            if script.string:
                match = re.search(ip_pattern, script.string)
                if match:
                    return match.group(1)
        
        return None
    
    def _extract_anonymity(self, cell) -> str:
        """Extract anonymity level from cell."""
        if not cell:
            return "Unknown"
        
        text = self._clean_text(cell.get_text()).lower()
        
        if 'elite' in text or 'high' in text:
            return "Elite"
        elif 'anonymous' in text:
            return "Anonymous"
        elif 'transparent' in text:
            return "Transparent"
        
        return "Unknown"


class SpysOneScraper(BaseScraper):
    """Scraper for Spys.One Russian proxy list."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from Spys.One."""
        self.logger.info("Scraping proxies from Spys.One")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find proxy data - Spys.One uses a specific table structure
            proxy_rows = soup.find_all('tr')
            
            for row in proxy_rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 4:
                        continue
                    
                    # First cell contains IP:Port
                    ip_port_text = self._clean_text(cells[0].get_text())
                    if ':' not in ip_port_text:
                        continue
                    
                    ip, port = self._parse_proxy_string(ip_port_text)
                    
                    # Extract proxy type
                    proxy_type = self._clean_text(cells[1].get_text()).upper()
                    if proxy_type not in ["HTTP", "HTTPS", "SOCKS4", "SOCKS5"]:
                        proxy_type = "HTTP"
                    
                    # Extract anonymity
                    anonymity_text = self._clean_text(cells[2].get_text())
                    anonymity = self._map_spys_anonymity(anonymity_text)
                    
                    # Extract additional info if available
                    uptime = None
                    speed = None
                    
                    if len(cells) > 6:
                        # Try to extract uptime percentage
                        uptime_text = self._clean_text(cells[6].get_text())
                        uptime_match = re.search(r'(\d+)%', uptime_text)
                        if uptime_match:
                            uptime = float(uptime_match.group(1))
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type=proxy_type,
                        country="RU",
                        anonymity=anonymity,
                        source=self.name,
                        uptime=uptime,
                        speed=speed
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing Spys.One row: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} proxies from Spys.One")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape Spys.One: {e}")
            raise
        
        return proxies
    
    def _map_spys_anonymity(self, anonymity_text: str) -> str:
        """Map Spys.One anonymity codes to standard levels."""
        anonymity_text = anonymity_text.upper()
        
        if 'HIA' in anonymity_text:
            return "Elite"
        elif 'ANM' in anonymity_text:
            return "Anonymous"
        elif 'NOA' in anonymity_text:
            return "Transparent"
        
        return "Unknown"


class FreeProxyListScraper(BaseScraper):
    """Scraper for Free-Proxy-List.net."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from Free-Proxy-List.net."""
        self.logger.info("Scraping proxies from Free-Proxy-List.net")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the proxy table
            proxy_table = soup.find('table', {'id': 'proxylisttable'})
            if not proxy_table:
                # Try alternative selector
                proxy_table = soup.find('table')
            
            if not proxy_table:
                self.logger.warning("Could not find proxy table on Free-Proxy-List.net")
                return proxies
            
            tbody = proxy_table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
            else:
                rows = proxy_table.find_all('tr')[1:]  # Skip header
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 7:
                        continue
                    
                    ip = self._clean_text(cells[0].get_text())
                    port_text = self._clean_text(cells[1].get_text())
                    country_code = self._clean_text(cells[2].get_text())
                    # country_name = self._clean_text(cells[3].get_text())  # Not used
                    anonymity = self._clean_text(cells[4].get_text())
                    # google = self._clean_text(cells[5].get_text())  # Not used
                    https = self._clean_text(cells[6].get_text())
                    
                    # Filter for Russian proxies only
                    if country_code != "RU":
                        continue
                    
                    if not port_text.isdigit():
                        continue
                    
                    port = int(port_text)
                    
                    # Determine proxy type
                    proxy_type = "HTTPS" if https.lower() == "yes" else "HTTP"
                    
                    # Map anonymity
                    if anonymity.lower() == "elite proxy":
                        anonymity = "Elite"
                    elif anonymity.lower() == "anonymous":
                        anonymity = "Anonymous"
                    else:
                        anonymity = "Transparent"
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type=proxy_type,
                        country="RU",
                        anonymity=anonymity,
                        source=self.name
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing Free-Proxy-List row: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} proxies from Free-Proxy-List.net")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape Free-Proxy-List.net: {e}")
            raise
        
        return proxies


class GeoNodeScraper(BaseScraper):
    """Scraper for GeoNode API."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from GeoNode API."""
        self.logger.info("Scraping proxies from GeoNode API")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            data = response.json()
            
            if 'data' not in data:
                self.logger.warning("No data field in GeoNode API response")
                return proxies
            
            for proxy_data in data['data']:
                try:
                    ip = proxy_data.get('ip', '').strip()
                    port = proxy_data.get('port')
                    
                    if not ip or not port:
                        continue
                    
                    # Extract proxy type from protocols
                    protocols = proxy_data.get('protocols', [])
                    proxy_type = "HTTP"
                    if protocols:
                        if 'socks5' in protocols:
                            proxy_type = "SOCKS5"
                        elif 'socks4' in protocols:
                            proxy_type = "SOCKS4"
                        elif 'https' in protocols:
                            proxy_type = "HTTPS"
                        elif 'http' in protocols:
                            proxy_type = "HTTP"
                    
                    # Extract anonymity level
                    anonymity_level = proxy_data.get('anonymityLevel', 'unknown').lower()
                    if anonymity_level == 'elite':
                        anonymity = "Elite"
                    elif anonymity_level == 'anonymous':
                        anonymity = "Anonymous"
                    elif anonymity_level == 'transparent':
                        anonymity = "Transparent"
                    else:
                        anonymity = "Unknown"
                    
                    # Extract additional information
                    uptime = proxy_data.get('upTime')
                    speed = proxy_data.get('speed')
                    response_time = proxy_data.get('responseTime')
                    
                    # Convert response time from milliseconds to seconds
                    if response_time:
                        response_time = response_time / 1000.0
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=int(port),
                        type=proxy_type,
                        country="RU",
                        anonymity=anonymity,
                        response_time=response_time,
                        source=self.name,
                        uptime=uptime,
                        speed=speed
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing GeoNode proxy data: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} proxies from GeoNode API")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape GeoNode API: {e}")
            raise
        
        return proxies


class HideMyScraper(BaseScraper):
    """Scraper for HideMy.name proxy list."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from HideMy.name."""
        self.logger.info("Scraping proxies from HideMy.name")
        proxies = []
        
        try:
            # HideMy.name requires JavaScript, so this is a basic implementation
            # In a production environment, you might want to use Selenium
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for proxy table
            proxy_table = soup.find('table', class_='proxy__t')
            if not proxy_table:
                # Try alternative selectors
                proxy_table = soup.find('table')
            
            if not proxy_table:
                self.logger.warning("Could not find proxy table on HideMy.name (may require JavaScript)")
                return proxies
            
            tbody = proxy_table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
            else:
                rows = proxy_table.find_all('tr')[1:]  # Skip header
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 6:
                        continue
                    
                    ip = self._clean_text(cells[0].get_text())
                    port_text = self._clean_text(cells[1].get_text())
                    # country = self._clean_text(cells[2].get_text())  # Not used, we know it's RU
                    speed_text = self._clean_text(cells[3].get_text())
                    proxy_type = self._clean_text(cells[4].get_text()).upper()
                    anonymity_text = self._clean_text(cells[5].get_text())
                    
                    if not port_text.isdigit():
                        continue
                    
                    port = int(port_text)
                    
                    # Map proxy type
                    if proxy_type not in ["HTTP", "HTTPS", "SOCKS4", "SOCKS5"]:
                        proxy_type = "HTTP"
                    
                    # Map anonymity
                    anonymity = self._map_hidemy_anonymity(anonymity_text)
                    
                    # Extract speed if available
                    speed = None
                    speed_match = re.search(r'(\d+)', speed_text)
                    if speed_match:
                        speed = int(speed_match.group(1))
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type=proxy_type,
                        country="RU",
                        anonymity=anonymity,
                        source=self.name,
                        speed=speed
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing HideMy.name row: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} proxies from HideMy.name")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape HideMy.name: {e}")
            raise
        
        return proxies
    
    def _map_hidemy_anonymity(self, anonymity_text: str) -> str:
        """Map HideMy.name anonymity levels."""
        anonymity_text = anonymity_text.lower()
        
        if 'high' in anonymity_text or 'elite' in anonymity_text:
            return "Elite"
        elif 'medium' in anonymity_text or 'anonymous' in anonymity_text:
            return "Anonymous"
        elif 'low' in anonymity_text or 'transparent' in anonymity_text:
            return "Transparent"
        
        return "Unknown"
