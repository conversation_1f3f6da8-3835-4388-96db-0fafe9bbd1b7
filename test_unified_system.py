#!/usr/bin/env python3
"""
Comprehensive Test Suite for Unified Proxy System
Tests all functionality including collection, validation, and server operations.
"""

import sys
import time
import subprocess
import threading
import requests
from pathlib import Path


def test_imports():
    """Test that the unified system can be imported."""
    print("Testing imports...")
    
    try:
        from unified_proxy_system import (
            UnifiedProxySystem, ProxyCollector, ProxyValidator,
            ProxyServer, TestClient, ProxyInfo
        )
        print("✅ All components imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_proxy_info_class():
    """Test the ProxyInfo data class."""
    print("\nTesting ProxyInfo class...")
    
    try:
        from unified_proxy_system import ProxyInfo
        
        # Test valid proxy creation
        proxy = ProxyInfo(
            ip="***********",
            port=8080,
            type="HTTP",
            country="CN",
            anonymity="Elite",
            status="Valid"
        )
        
        assert proxy.is_healthy == True
        print("✅ ProxyInfo creation and health check work")
        
        # Test statistics
        proxy.record_success()
        assert proxy.success_count == 1
        assert proxy.total_requests == 1
        
        proxy.record_failure()
        assert proxy.failures == 1
        assert proxy.total_requests == 2
        
        print("✅ ProxyInfo statistics work correctly")
        
        # Test dictionary conversion
        proxy_dict = proxy.to_dict()
        assert 'ip' in proxy_dict
        assert 'port' in proxy_dict
        print("✅ ProxyInfo dictionary conversion works")
        
        return True
        
    except Exception as e:
        print(f"❌ ProxyInfo test failed: {e}")
        return False


def test_chinese_proxy_collection():
    """Test Chinese proxy collection."""
    print("\nTesting Chinese proxy collection...")
    
    try:
        from unified_proxy_system import ProxyCollector
        
        collector = ProxyCollector("CN")
        proxies = collector.collect_proxies(sources=["geonode"])
        
        if proxies:
            print(f"✅ Collected {len(proxies)} Chinese proxies")
            
            # Check proxy properties
            sample_proxy = proxies[0]
            assert sample_proxy.country == "CN"
            assert sample_proxy.ip
            assert sample_proxy.port > 0
            print("✅ Chinese proxy properties are valid")
            
            return True
        else:
            print("⚠ No Chinese proxies collected (may be normal)")
            return True
            
    except Exception as e:
        print(f"❌ Chinese proxy collection failed: {e}")
        return False


def test_russian_proxy_collection():
    """Test Russian proxy collection."""
    print("\nTesting Russian proxy collection...")
    
    try:
        from unified_proxy_system import ProxyCollector
        
        collector = ProxyCollector("RU")
        proxies = collector.collect_proxies(sources=["geonode"])
        
        if proxies:
            print(f"✅ Collected {len(proxies)} Russian proxies")
            
            # Check proxy properties
            sample_proxy = proxies[0]
            assert sample_proxy.country == "RU"
            assert sample_proxy.ip
            assert sample_proxy.port > 0
            print("✅ Russian proxy properties are valid")
            
            return True
        else:
            print("⚠ No Russian proxies collected (may be normal)")
            return True
            
    except Exception as e:
        print(f"❌ Russian proxy collection failed: {e}")
        return False


def test_csv_operations():
    """Test CSV save and load operations."""
    print("\nTesting CSV operations...")
    
    try:
        from unified_proxy_system import ProxyCollector, ProxyInfo
        
        # Create test proxies
        test_proxies = [
            ProxyInfo(ip="*******", port=8080, type="HTTP", country="CN", status="Valid"),
            ProxyInfo(ip="*******", port=1080, type="SOCKS5", country="RU", status="Valid")
        ]
        
        collector = ProxyCollector()
        
        # Test save
        test_file = "test_proxies.csv"
        collector.save_to_csv(test_proxies, test_file)
        
        if Path(test_file).exists():
            print("✅ CSV save operation successful")
        else:
            print("❌ CSV file not created")
            return False
        
        # Test load
        loaded_proxies = collector.load_from_csv(test_file)
        
        if len(loaded_proxies) == len(test_proxies):
            print("✅ CSV load operation successful")
        else:
            print(f"❌ Loaded {len(loaded_proxies)} proxies, expected {len(test_proxies)}")
            return False
        
        # Cleanup
        Path(test_file).unlink()
        print("✅ CSV cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV operations failed: {e}")
        return False


def test_proxy_server_initialization():
    """Test proxy server initialization."""
    print("\nTesting proxy server initialization...")
    
    try:
        from unified_proxy_system import ProxyServer, ProxyInfo
        
        # Create test proxies
        test_proxies = [
            ProxyInfo(ip="*******", port=8080, type="HTTP", country="CN", status="Valid"),
            ProxyInfo(ip="*******", port=1080, type="SOCKS5", country="RU", status="Valid")
        ]
        
        # Initialize server
        server = ProxyServer(test_proxies)
        
        assert server.pool_manager is not None
        assert server.http_handler is not None
        assert len(server.pool_manager.healthy_proxies) == 2
        
        print("✅ Proxy server initialization successful")
        
        # Test statistics
        stats = server.get_statistics()
        assert 'start_time' in stats
        assert 'healthy_proxies' in stats
        print("✅ Server statistics work correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Proxy server initialization failed: {e}")
        return False


def test_test_client():
    """Test the built-in test client."""
    print("\nTesting built-in test client...")
    
    try:
        from unified_proxy_system import TestClient
        
        # Initialize test client
        test_client = TestClient()
        
        # Test direct IP retrieval
        original_ip = test_client._get_external_ip_direct()
        
        if original_ip:
            print(f"✅ Retrieved external IP: {original_ip}")
            return True
        else:
            print("⚠ Could not retrieve external IP (may be normal)")
            return True
            
    except Exception as e:
        print(f"❌ Test client failed: {e}")
        return False


def test_cli_interface():
    """Test command-line interface."""
    print("\nTesting CLI interface...")
    
    try:
        # Test help command
        result = subprocess.run([
            sys.executable, "unified_proxy_system.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "Unified Proxy System" in result.stdout:
            print("✅ CLI help command works")
        else:
            print("❌ CLI help command failed")
            return False
        
        # Test invalid arguments
        result = subprocess.run([
            sys.executable, "unified_proxy_system.py", "--invalid-arg"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("✅ CLI properly rejects invalid arguments")
        else:
            print("❌ CLI should reject invalid arguments")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CLI interface test failed: {e}")
        return False


def test_unified_system_integration():
    """Test the main UnifiedProxySystem class."""
    print("\nTesting UnifiedProxySystem integration...")
    
    try:
        from unified_proxy_system import UnifiedProxySystem
        
        # Initialize system
        system = UnifiedProxySystem()
        
        # Test proxy collection
        proxies = system.collect_proxies("CN", sources=["geonode"])
        
        if proxies:
            print(f"✅ Integrated collection successful: {len(proxies)} proxies")
            
            # Test CSV operations
            test_file = "integration_test.csv"
            system.save_proxies(proxies[:5], test_file)  # Save first 5 proxies
            
            loaded_proxies = system.load_proxies(test_file)
            
            if len(loaded_proxies) == 5:
                print("✅ Integrated CSV operations successful")
            else:
                print(f"❌ Expected 5 proxies, got {len(loaded_proxies)}")
                return False
            
            # Cleanup
            Path(test_file).unlink()
            
            return True
        else:
            print("⚠ No proxies collected for integration test")
            return True
            
    except Exception as e:
        print(f"❌ UnifiedProxySystem integration failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Unified Proxy System - Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_proxy_info_class,
        test_chinese_proxy_collection,
        test_russian_proxy_collection,
        test_csv_operations,
        test_proxy_server_initialization,
        test_test_client,
        test_cli_interface,
        test_unified_system_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"💥 Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Unified Proxy System is fully functional.")
        return 0
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
