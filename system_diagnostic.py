#!/usr/bin/env python3
"""
Comprehensive diagnostic script for proxy validation system.
Tests all components systematically to identify issues.
"""

import requests
import json
import time
import urllib3
from typing import Dict, List, Any

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_socks_dependencies():
    """Test SOCKS dependencies are working."""
    print("🔍 Testing SOCKS Dependencies...")
    try:
        import socks
        print(f"✅ PySocks installed: version {socks.__version__}")
        
        import requests
        print(f"✅ Requests installed: version {requests.__version__}")
        
        # Test SOCKS URL construction
        test_proxy_url = "socks4://127.0.0.1:1080"
        print(f"✅ SOCKS URL format test: {test_proxy_url}")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def test_geonode_api():
    """Test GeoNode API is returning valid data."""
    print("\n🔍 Testing GeoNode API...")
    try:
        url = "https://proxylist.geonode.com/api/proxy-list?limit=5&page=1&sort_by=lastChecked&sort_type=desc&country=RU"
        print(f"📡 Making request to: {url}")
        
        response = requests.get(url, timeout=30)
        print(f"✅ API Response: Status {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            proxies = data.get('data', [])
            print(f"✅ Retrieved {len(proxies)} proxies")
            
            # Analyze proxy data
            if proxies:
                sample_proxy = proxies[0]
                print(f"📊 Sample proxy data:")
                print(f"   IP: {sample_proxy.get('ip')}")
                print(f"   Port: {sample_proxy.get('port')}")
                print(f"   Protocols: {sample_proxy.get('protocols', [])}")
                print(f"   Anonymity: {sample_proxy.get('anonymityLevel')}")
                print(f"   Uptime: {sample_proxy.get('upTime')}%")
                print(f"   Response Time: {sample_proxy.get('responseTime')}ms")
                print(f"   Last Checked: {sample_proxy.get('lastChecked')}")
                
                # Check protocol distribution
                protocol_counts = {}
                for proxy in proxies:
                    protocols = proxy.get('protocols', [])
                    for protocol in protocols:
                        protocol_counts[protocol] = protocol_counts.get(protocol, 0) + 1
                
                print(f"📊 Protocol distribution: {protocol_counts}")
                return proxies
        else:
            print(f"❌ API Error: Status {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ API Error: {e}")
        return []

def test_proxy_url_construction(proxies: List[Dict]):
    """Test proxy URL construction logic."""
    print("\n🔍 Testing Proxy URL Construction...")
    
    for i, proxy_data in enumerate(proxies[:3]):  # Test first 3
        ip = proxy_data.get('ip')
        port = proxy_data.get('port')
        protocols = proxy_data.get('protocols', [])
        
        print(f"\n--- Proxy {i+1}: {ip}:{port} ---")
        print(f"Available protocols: {protocols}")
        
        # Test URL construction for each protocol
        for protocol in protocols:
            if protocol.lower() == 'socks4':
                proxy_url = f"socks4://{ip}:{port}"
            elif protocol.lower() == 'socks5':
                proxy_url = f"socks5://{ip}:{port}"
            elif protocol.lower() == 'https':
                proxy_url = f"https://{ip}:{port}"
            else:
                proxy_url = f"http://{ip}:{port}"
            
            print(f"   {protocol} -> {proxy_url}")

def test_direct_validation(proxies: List[Dict], limit: int = 3):
    """Test direct proxy validation with detailed logging."""
    print(f"\n🔍 Testing Direct Proxy Validation (limit: {limit})...")
    
    test_urls = [
        "http://httpbin.org/ip",
        "http://icanhazip.com"
    ]
    
    working_proxies = 0
    
    for i, proxy_data in enumerate(proxies[:limit]):
        ip = proxy_data.get('ip')
        port = proxy_data.get('port')
        protocols = proxy_data.get('protocols', [])
        uptime = proxy_data.get('upTime', 0)
        
        print(f"\n--- Testing Proxy {i+1}: {ip}:{port} ---")
        print(f"Protocols: {protocols}, Uptime: {uptime}%")
        
        # Test with the first available protocol
        if protocols:
            protocol = protocols[0].lower()
            if protocol == 'socks4':
                proxy_url = f"socks4://{ip}:{port}"
            elif protocol == 'socks5':
                proxy_url = f"socks5://{ip}:{port}"
            elif protocol == 'https':
                proxy_url = f"https://{ip}:{port}"
            else:
                proxy_url = f"http://{ip}:{port}"
            
            print(f"Using proxy URL: {proxy_url}")
            
            proxies_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # Test with each URL
            proxy_working = False
            for test_url in test_urls:
                print(f"  Testing with {test_url}...")
                
                try:
                    start_time = time.time()
                    response = requests.get(
                        test_url,
                        proxies=proxies_dict,
                        timeout=10,
                        verify=False,
                        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                    )
                    response_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        print(f"    ✅ SUCCESS: Status {response.status_code}, Time: {response_time:.2f}s")
                        print(f"    Response: {response.text[:100]}")
                        proxy_working = True
                        break
                    else:
                        print(f"    ⚠ HTTP Error: Status {response.status_code}, Time: {response_time:.2f}s")
                        
                except requests.exceptions.Timeout:
                    response_time = time.time() - start_time
                    print(f"    ❌ Timeout after {response_time:.2f}s")
                except requests.exceptions.ConnectionError as e:
                    print(f"    ❌ Connection Error: {str(e)[:100]}")
                except Exception as e:
                    print(f"    ❌ Other Error: {str(e)[:100]}")
            
            if proxy_working:
                working_proxies += 1
                print(f"  🎉 PROXY WORKS!")
            else:
                print(f"  ❌ Proxy failed all tests")
    
    print(f"\n📊 Results: {working_proxies}/{limit} proxies working ({working_proxies/limit*100:.1f}%)")
    return working_proxies

def main():
    """Run comprehensive diagnostic."""
    print("🚀 PROXY VALIDATION SYSTEM DIAGNOSTIC")
    print("=" * 50)
    
    # Step 1: Test dependencies
    if not test_socks_dependencies():
        print("❌ Dependency test failed. Exiting.")
        return
    
    # Step 2: Test GeoNode API
    proxies = test_geonode_api()
    if not proxies:
        print("❌ GeoNode API test failed. Exiting.")
        return
    
    # Step 3: Test URL construction
    test_proxy_url_construction(proxies)
    
    # Step 4: Test direct validation
    working_count = test_direct_validation(proxies, limit=5)
    
    print(f"\n🎯 DIAGNOSTIC SUMMARY:")
    print(f"✅ Dependencies: OK")
    print(f"✅ GeoNode API: OK ({len(proxies)} proxies retrieved)")
    print(f"✅ URL Construction: OK")
    print(f"{'✅' if working_count > 0 else '❌'} Proxy Validation: {working_count}/5 working")
    
    if working_count == 0:
        print("\n🔍 POSSIBLE ISSUES:")
        print("- All tested proxies may be genuinely non-functional")
        print("- Network connectivity issues (firewall, ISP blocking)")
        print("- Validation timeout too short for slow proxies")
        print("- Geographic restrictions on proxy usage")

if __name__ == "__main__":
    main()
