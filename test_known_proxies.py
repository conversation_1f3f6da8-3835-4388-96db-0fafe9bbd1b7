#!/usr/bin/env python3
"""
Test with known working proxy lists to isolate the issue.
"""

import requests
import time
import urllib3

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_free_proxy_list():
    """Test proxies from free-proxy-list.net API."""
    print("🔍 Testing proxies from free-proxy-list.net...")
    
    try:
        # This is a different proxy API
        response = requests.get("https://www.proxy-list.download/api/v1/get?type=http&anon=elite&country=US", timeout=30)
        
        if response.status_code == 200:
            proxy_lines = response.text.strip().split('\n')
            print(f"Retrieved {len(proxy_lines)} proxy lines")
            
            for i, line in enumerate(proxy_lines[:5]):  # Test first 5
                if ':' in line:
                    ip, port = line.strip().split(':')
                    print(f"\nTesting proxy {i+1}: {ip}:{port}")
                    
                    proxy_url = f"http://{ip}:{port}"
                    proxies = {'http': proxy_url, 'https': proxy_url}
                    
                    try:
                        response = requests.get(
                            "http://httpbin.org/ip",
                            proxies=proxies,
                            timeout=10,
                            verify=False
                        )
                        
                        if response.status_code == 200:
                            print(f"✅ WORKING: {response.text[:50]}")
                            return True
                        else:
                            print(f"❌ HTTP {response.status_code}")
                            
                    except requests.exceptions.Timeout:
                        print("❌ Timeout")
                    except Exception as e:
                        print(f"❌ Error: {str(e)[:100]}")
        else:
            print(f"❌ API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return False

def test_manual_proxies():
    """Test with manually specified proxies that should work."""
    print("\n🔍 Testing manually specified proxies...")
    
    # These are some commonly available public proxies (may or may not work)
    manual_proxies = [
        "*******:3128",      # Won't work, but tests our logic
        "*******:80",        # Won't work, but tests our logic  
        "proxy.example.com:8080",  # Won't work, but tests our logic
    ]
    
    for proxy_str in manual_proxies:
        ip, port = proxy_str.split(':')
        print(f"\nTesting manual proxy: {ip}:{port}")
        
        proxy_url = f"http://{ip}:{port}"
        proxies = {'http': proxy_url, 'https': proxy_url}
        
        try:
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=5,  # Short timeout for manual tests
                verify=False
            )
            
            if response.status_code == 200:
                print(f"✅ WORKING: {response.text[:50]}")
                return True
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ Timeout (expected)")
        except requests.exceptions.ConnectionError:
            print("❌ Connection refused (expected)")
        except Exception as e:
            print(f"❌ Error: {str(e)[:100]}")
    
    return False

def test_proxy_environment():
    """Test if there are system proxy settings interfering."""
    print("\n🔍 Testing proxy environment...")
    
    import os
    
    # Check for system proxy environment variables
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"⚠ Found system proxy: {var}={value}")
        else:
            print(f"✅ No {var} set")
    
    # Test with explicitly no proxy
    print("\nTesting with explicit no_proxy...")
    try:
        response = requests.get(
            "http://httpbin.org/ip",
            proxies={'http': None, 'https': None},
            timeout=10
        )
        if response.status_code == 200:
            print(f"✅ Direct (no proxy) works: {response.text[:50]}")
        else:
            print(f"❌ Direct failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Direct error: {e}")

def test_requests_proxy_logic():
    """Test the exact requests proxy logic we're using."""
    print("\n🔍 Testing requests proxy logic...")
    
    # Test with a fake proxy to see exact error behavior
    fake_proxy = "http://192.168.1.999:8080"  # Invalid IP
    proxies = {'http': fake_proxy, 'https': fake_proxy}
    
    print(f"Testing with fake proxy: {fake_proxy}")
    
    try:
        response = requests.get(
            "http://httpbin.org/ip",
            proxies=proxies,
            timeout=5
        )
        print(f"Unexpected success: {response.status_code}")
    except requests.exceptions.ConnectTimeout:
        print("✅ Got ConnectTimeout (expected)")
    except requests.exceptions.ConnectionError as e:
        print(f"✅ Got ConnectionError: {str(e)[:100]}")
    except Exception as e:
        print(f"✅ Got other error: {type(e).__name__}: {str(e)[:100]}")

def main():
    """Run all proxy tests."""
    print("🔍 PROXY TESTING - Isolating the issue")
    print("=" * 50)
    
    # Test environment
    test_proxy_environment()
    
    # Test requests logic
    test_requests_proxy_logic()
    
    # Test different proxy sources
    if test_free_proxy_list():
        print("\n🎉 Found working proxy from free-proxy-list!")
        return
    
    if test_manual_proxies():
        print("\n🎉 Found working manual proxy!")
        return
    
    print("\n🤔 CONCLUSION:")
    print("No working proxies found from any source.")
    print("This suggests either:")
    print("1. Network/firewall is blocking ALL proxy connections")
    print("2. ISP is blocking proxy traffic")
    print("3. All tested proxy sources have dead proxies")
    print("4. There's a fundamental issue with our proxy testing approach")

if __name__ == "__main__":
    main()
