"""
HTTP/HTTPS Proxy Handler for Russian Proxy Server
Handles HTTP CONNECT and regular HTTP requests with authentication and upstream forwarding.
"""

import socket
import threading
import time
import logging
import re
from typing import Optional, Tuple, Dict, Any
from urllib.parse import urlparse
from proxy_server_base import BaseProxyHand<PERSON>, ProxyServerConfig, AuthenticationManager, UpstreamProxy
from proxy_pool_manager import ProxyPoolManager


class HTTPProxyHandler(BaseProxyHandler):
    """Handles HTTP/HTTPS proxy connections."""
    
    def __init__(self, config: ProxyServerConfig, auth_manager: AuthenticationManager, 
                 pool_manager: ProxyPoolManager):
        """Initialize HTTP proxy handler."""
        super().__init__(config, auth_manager)
        self.pool_manager = pool_manager
        self.http_config = config.get_protocol_config('http')
        self.buffer_size = config.get('server', 'buffer_size', 8192)
        self.connect_timeout = self.http_config.get('connect_timeout', 15)
        self.max_request_size = self.http_config.get('max_request_size', 10485760)
    
    def handle_connection(self, client_socket: socket.socket, client_address: Tuple[str, int]) -> None:
        """Handle incoming HTTP proxy connection."""
        conn_id = self.start_connection_stats(client_address, 'HTTP')
        
        try:
            # Set socket timeout
            client_socket.settimeout(self.connect_timeout)
            
            # Read the HTTP request
            request_data = self._read_http_request(client_socket)
            if not request_data:
                self.logger.debug(f"No request data from {client_address[0]}")
                return
            
            # Parse the HTTP request
            request_lines = request_data.decode('utf-8', errors='ignore').split('\r\n')
            if not request_lines:
                self.logger.debug(f"Invalid request from {client_address[0]}")
                return
            
            request_line = request_lines[0]
            headers = self._parse_headers(request_lines[1:])
            
            # Check authentication
            if not self._authenticate_request(client_socket, headers):
                self.end_connection_stats(conn_id, "Authentication failed")
                return
            
            # Parse request method and URL
            try:
                method, url, version = request_line.split(' ', 2)
            except ValueError:
                self._send_error_response(client_socket, 400, "Bad Request")
                self.end_connection_stats(conn_id, "Bad request format")
                return
            
            self.logger.debug(f"HTTP request: {method} {url} from {client_address[0]}")
            
            # Handle different HTTP methods
            if method == 'CONNECT':
                self._handle_connect_request(client_socket, url, conn_id)
            else:
                self._handle_http_request(client_socket, method, url, version, headers, conn_id)
                
        except Exception as e:
            self.logger.error(f"Error handling HTTP connection from {client_address[0]}: {e}")
            self.end_connection_stats(conn_id, str(e))
        finally:
            self.close_socket_safely(client_socket)
    
    def _read_http_request(self, client_socket: socket.socket) -> Optional[bytes]:
        """Read HTTP request from client socket."""
        try:
            request_data = b''
            while b'\r\n\r\n' not in request_data:
                chunk = client_socket.recv(self.buffer_size)
                if not chunk:
                    break
                request_data += chunk
                
                # Prevent excessive memory usage
                if len(request_data) > self.max_request_size:
                    self.logger.warning("Request too large")
                    return None
            
            return request_data
            
        except socket.timeout:
            self.logger.debug("Timeout reading HTTP request")
            return None
        except Exception as e:
            self.logger.debug(f"Error reading HTTP request: {e}")
            return None
    
    def _parse_headers(self, header_lines: list) -> Dict[str, str]:
        """Parse HTTP headers."""
        headers = {}
        for line in header_lines:
            if ':' in line:
                key, value = line.split(':', 1)
                headers[key.strip().lower()] = value.strip()
        return headers
    
    def _authenticate_request(self, client_socket: socket.socket, headers: Dict[str, str]) -> bool:
        """Authenticate HTTP request."""
        if not self.auth_manager.enabled:
            return True
        
        auth_header = headers.get('proxy-authorization', '')
        
        if self.auth_manager.validate_http_auth(auth_header):
            return True
        
        # Send 407 Proxy Authentication Required
        response = (
            "HTTP/1.1 407 Proxy Authentication Required\r\n"
            f"Proxy-Authenticate: {self.auth_manager.get_http_auth_challenge()}\r\n"
            "Content-Length: 0\r\n"
            "\r\n"
        )
        
        try:
            client_socket.send(response.encode())
        except:
            pass
        
        return False
    
    def _handle_connect_request(self, client_socket: socket.socket, target: str, conn_id: str) -> None:
        """Handle HTTP CONNECT request for HTTPS tunneling."""
        try:
            # Parse target host and port
            if ':' in target:
                host, port_str = target.rsplit(':', 1)
                port = int(port_str)
            else:
                host = target
                port = 443  # Default HTTPS port
            
            self.logger.debug(f"CONNECT request to {host}:{port}")
            
            # Get upstream proxy
            upstream_proxy = self.pool_manager.get_proxy()
            if not upstream_proxy:
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return
            
            # Connect through upstream proxy
            upstream_socket = self._connect_through_upstream(upstream_proxy, host, port)
            if not upstream_socket:
                self.pool_manager.record_proxy_failure(upstream_proxy)
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return
            
            # Send 200 Connection Established
            response = "HTTP/1.1 200 Connection established\r\n\r\n"
            client_socket.send(response.encode())
            
            # Update statistics
            self.update_connection_stats(conn_id, upstream_proxy=str(upstream_proxy))
            self.pool_manager.record_proxy_success(upstream_proxy)
            
            # Start tunneling
            self._tunnel_data(client_socket, upstream_socket, conn_id)
            
        except Exception as e:
            self.logger.error(f"Error handling CONNECT request: {e}")
            self._send_error_response(client_socket, 500, "Internal Server Error")
    
    def _handle_http_request(self, client_socket: socket.socket, method: str, url: str, 
                           version: str, headers: Dict[str, str], conn_id: str) -> None:
        """Handle regular HTTP request."""
        try:
            # Parse URL
            parsed_url = urlparse(url)
            if not parsed_url.hostname:
                self._send_error_response(client_socket, 400, "Bad Request")
                return
            
            host = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
            
            self.logger.debug(f"HTTP request: {method} {url}")
            
            # Get upstream proxy
            upstream_proxy = self.pool_manager.get_proxy()
            if not upstream_proxy:
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return
            
            # Forward request through upstream proxy
            success = self._forward_http_request(client_socket, upstream_proxy, method, url, 
                                               version, headers, conn_id)
            
            if success:
                self.pool_manager.record_proxy_success(upstream_proxy)
            else:
                self.pool_manager.record_proxy_failure(upstream_proxy)
                
        except Exception as e:
            self.logger.error(f"Error handling HTTP request: {e}")
            self._send_error_response(client_socket, 500, "Internal Server Error")
    
    def _connect_through_upstream(self, upstream_proxy: UpstreamProxy, target_host: str, 
                                target_port: int) -> Optional[socket.socket]:
        """Connect to target through upstream proxy."""
        try:
            # Create connection to upstream proxy
            upstream_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            upstream_socket.settimeout(self.connect_timeout)
            upstream_socket.connect((upstream_proxy.ip, upstream_proxy.port))
            
            if upstream_proxy.protocol in ['HTTP', 'HTTPS']:
                # Use HTTP CONNECT method
                connect_request = (
                    f"CONNECT {target_host}:{target_port} HTTP/1.1\r\n"
                    f"Host: {target_host}:{target_port}\r\n"
                    "\r\n"
                )
                
                upstream_socket.send(connect_request.encode())
                
                # Read response
                response = upstream_socket.recv(self.buffer_size).decode()
                if "200" not in response.split('\r\n')[0]:
                    upstream_socket.close()
                    return None
                
                return upstream_socket
                
            elif upstream_proxy.protocol == 'SOCKS5':
                # Implement SOCKS5 connection
                return self._connect_socks5(upstream_socket, target_host, target_port)
            
            else:
                upstream_socket.close()
                return None
                
        except Exception as e:
            self.logger.debug(f"Failed to connect through upstream {upstream_proxy}: {e}")
            return None
    
    def _connect_socks5(self, sock: socket.socket, host: str, port: int) -> Optional[socket.socket]:
        """Connect through SOCKS5 proxy."""
        try:
            # SOCKS5 greeting
            sock.send(b'\x05\x01\x00')  # Version 5, 1 method, no auth
            response = sock.recv(2)
            
            if response != b'\x05\x00':
                return None
            
            # SOCKS5 connect request
            request = b'\x05\x01\x00'  # Version 5, connect, reserved
            
            # Add destination address
            try:
                # Try to parse as IP address
                import ipaddress
                ip = ipaddress.ip_address(host)
                if ip.version == 4:
                    request += b'\x01' + socket.inet_aton(host)
                else:
                    request += b'\x04' + socket.inet_pton(socket.AF_INET6, host)
            except:
                # Use domain name
                host_bytes = host.encode()
                request += b'\x03' + bytes([len(host_bytes)]) + host_bytes
            
            # Add port
            request += port.to_bytes(2, 'big')
            
            sock.send(request)
            response = sock.recv(10)
            
            if len(response) < 2 or response[1] != 0:
                return None
            
            return sock
            
        except Exception as e:
            self.logger.debug(f"SOCKS5 connection failed: {e}")
            return None
    
    def _forward_http_request(self, client_socket: socket.socket, upstream_proxy: UpstreamProxy,
                            method: str, url: str, version: str, headers: Dict[str, str], 
                            conn_id: str) -> bool:
        """Forward HTTP request through upstream proxy."""
        try:
            # Connect to upstream proxy
            upstream_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            upstream_socket.settimeout(self.connect_timeout)
            upstream_socket.connect((upstream_proxy.ip, upstream_proxy.port))
            
            # Reconstruct HTTP request
            request_lines = [f"{method} {url} {version}"]
            
            # Add headers (remove proxy-specific headers)
            for key, value in headers.items():
                if key not in ['proxy-authorization', 'proxy-connection']:
                    request_lines.append(f"{key.title()}: {value}")
            
            request = '\r\n'.join(request_lines) + '\r\n\r\n'
            upstream_socket.send(request.encode())
            
            # Update statistics
            self.update_connection_stats(conn_id, upstream_proxy=str(upstream_proxy))
            
            # Forward response back to client
            self._tunnel_data(client_socket, upstream_socket, conn_id)
            
            return True
            
        except Exception as e:
            self.logger.debug(f"Failed to forward HTTP request through {upstream_proxy}: {e}")
            return False
    
    def _tunnel_data(self, client_socket: socket.socket, upstream_socket: socket.socket, 
                    conn_id: str) -> None:
        """Tunnel data between client and upstream sockets."""
        def forward_data(source: socket.socket, destination: socket.socket, direction: str):
            try:
                while True:
                    data = source.recv(self.buffer_size)
                    if not data:
                        break
                    
                    destination.send(data)
                    
                    # Update statistics
                    if direction == 'client_to_upstream':
                        self.update_connection_stats(conn_id, bytes_sent=len(data))
                    else:
                        self.update_connection_stats(conn_id, bytes_received=len(data))
                        
            except Exception as e:
                self.logger.debug(f"Tunneling error ({direction}): {e}")
            finally:
                self.close_socket_safely(source)
                self.close_socket_safely(destination)
        
        # Start forwarding threads
        client_to_upstream = threading.Thread(
            target=forward_data,
            args=(client_socket, upstream_socket, 'client_to_upstream'),
            daemon=True
        )
        
        upstream_to_client = threading.Thread(
            target=forward_data,
            args=(upstream_socket, client_socket, 'upstream_to_client'),
            daemon=True
        )
        
        client_to_upstream.start()
        upstream_to_client.start()
        
        # Wait for both threads to complete
        client_to_upstream.join()
        upstream_to_client.join()
    
    def _send_error_response(self, client_socket: socket.socket, status_code: int, 
                           status_text: str) -> None:
        """Send HTTP error response."""
        response = (
            f"HTTP/1.1 {status_code} {status_text}\r\n"
            "Content-Length: 0\r\n"
            "Connection: close\r\n"
            "\r\n"
        )
        
        try:
            client_socket.send(response.encode())
        except:
            pass
