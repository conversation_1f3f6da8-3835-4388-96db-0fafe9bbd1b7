{"server": {"http_port": 8080, "socks_port": 1080, "bind_address": "127.0.0.1", "max_connections": 1000, "connection_timeout": 30, "buffer_size": 8192}, "authentication": {"enabled": true, "username": "proxyuser", "password": "proxypass", "realm": "Russian Proxy Server"}, "upstream_proxies": {"csv_file": "russian_proxies.csv", "selection_mode": "round_robin", "health_check_interval": 300, "health_check_timeout": 10, "max_failures": 3, "retry_failed_after": 600, "low_latency_threshold": 2.0, "auto_reload": true, "reload_interval": 3600}, "protocols": {"http": {"enabled": true, "keep_alive": true, "max_request_size": 10485760, "connect_timeout": 15}, "socks5": {"enabled": true, "auth_methods": ["username_password"], "connect_timeout": 15, "resolve_dns": true}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "proxy_server.log", "max_size": "50MB", "backup_count": 5, "console_output": true}, "statistics": {"enabled": true, "update_interval": 5, "history_size": 1000, "export_interval": 300, "export_file": "proxy_stats.json"}, "security": {"max_connections_per_ip": 50, "rate_limit_requests": 1000, "rate_limit_window": 3600, "blocked_domains": [], "allowed_domains": [], "block_private_ips": false}}