#!/usr/bin/env python3
"""
Investigate HTTP response codes from proxy connections.
The key insight: we got HTTP 409 from *******:80, which means the proxy connection worked!
"""

import requests
import urllib3

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_cloudflare_proxy():
    """Test *******:80 which gave us HTTP 409."""
    print("🔍 Investigating *******:80 (Cloudflare DNS) as proxy...")
    
    proxy_url = "http://*******:80"
    proxies = {'http': proxy_url, 'https': proxy_url}
    
    test_urls = [
        "http://httpbin.org/ip",
        "http://icanhazip.com", 
        "http://example.com",
        "http://google.com"
    ]
    
    for url in test_urls:
        print(f"\nTesting {url} through *******:80...")
        try:
            response = requests.get(
                url,
                proxies=proxies,
                timeout=10,
                verify=False,
                allow_redirects=False  # Don't follow redirects
            )
            
            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(list(response.headers.items())[:3])}")
            print(f"Content preview: {response.text[:100]}")
            
            # Check if this is actually a working proxy
            if response.status_code in [200, 301, 302, 403, 404]:
                print("✅ This might be a working proxy (got valid HTTP response)!")
                
        except Exception as e:
            print(f"❌ Error: {str(e)[:100]}")

def test_validation_criteria():
    """Test what our current validation considers 'success'."""
    print("\n🔍 Testing current validation criteria...")
    
    # Check what the unified system considers success
    print("Current validation logic:")
    print("- Only HTTP 200 is considered success")
    print("- All other codes (301, 302, 403, 404, 409, etc.) are failures")
    print("- This might be too strict!")
    
    # Test different response codes
    test_cases = [
        ("http://httpbin.org/status/200", "Should succeed"),
        ("http://httpbin.org/status/301", "Redirect - should this succeed?"),
        ("http://httpbin.org/status/403", "Forbidden - proxy worked but site blocked"),
        ("http://httpbin.org/status/404", "Not found - proxy worked but page missing"),
    ]
    
    print("\nTesting different HTTP status codes directly (no proxy):")
    for url, description in test_cases:
        try:
            response = requests.get(url, timeout=10, allow_redirects=False)
            print(f"{response.status_code}: {description}")
        except Exception as e:
            print(f"Error testing {url}: {e}")

def test_better_validation_logic():
    """Test with improved validation logic."""
    print("\n🔍 Testing improved validation logic...")
    
    # Get a proxy from GeoNode
    try:
        api_url = "https://proxylist.geonode.com/api/proxy-list?limit=1&page=1&sort_by=lastChecked&sort_type=desc&country=US"
        response = requests.get(api_url, timeout=30)
        
        if response.status_code != 200:
            print("❌ Couldn't get proxy from GeoNode")
            return
            
        data = response.json()
        proxies_data = data.get('data', [])
        
        if not proxies_data:
            print("❌ No proxies returned")
            return
            
        proxy_data = proxies_data[0]
        ip = proxy_data.get('ip')
        port = proxy_data.get('port')
        protocols = proxy_data.get('protocols', [])
        
        print(f"Testing proxy: {ip}:{port} ({protocols})")
        
        # Test with improved validation logic
        if 'http' in protocols:
            proxy_url = f"http://{ip}:{port}"
        elif 'socks4' in protocols:
            proxy_url = f"socks4://{ip}:{port}"
        elif 'socks5' in protocols:
            proxy_url = f"socks5://{ip}:{port}"
        else:
            proxy_url = f"http://{ip}:{port}"
            
        proxies = {'http': proxy_url, 'https': proxy_url}
        
        test_urls = [
            "http://httpbin.org/ip",
            "http://icanhazip.com",
            "http://example.com"
        ]
        
        for url in test_urls:
            print(f"\nTesting {url}...")
            try:
                response = requests.get(
                    url,
                    proxies=proxies,
                    timeout=15,
                    verify=False,
                    allow_redirects=True
                )
                
                print(f"Status: {response.status_code}")
                
                # IMPROVED VALIDATION LOGIC:
                # Consider more status codes as "success"
                if response.status_code in [200, 301, 302, 403]:
                    print("✅ PROXY WORKS (improved logic)!")
                    print(f"Response: {response.text[:100]}")
                    return True
                elif response.status_code in [404, 409, 500, 502, 503]:
                    print("⚠ Proxy connected but got error response")
                    print("This still means the proxy is functional!")
                else:
                    print(f"❌ Unexpected status: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print("❌ Timeout")
            except Exception as e:
                print(f"❌ Error: {str(e)[:100]}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return False

def main():
    """Run investigation."""
    print("🔍 HTTP RESPONSE CODE INVESTIGATION")
    print("=" * 50)
    
    # Test the Cloudflare case that gave us HTTP 409
    test_cloudflare_proxy()
    
    # Understand current validation criteria
    test_validation_criteria()
    
    # Test with better validation logic
    if test_better_validation_logic():
        print("\n🎉 FOUND THE BUG!")
        print("The validation logic is too strict!")
        print("It only accepts HTTP 200, but proxies can return other valid codes.")
    else:
        print("\n🤔 Still investigating...")

if __name__ == "__main__":
    main()
