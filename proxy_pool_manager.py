"""
Upstream Proxy Pool Manager for Russian Proxy Server
Manages the pool of upstream Russian proxies with health checking and selection strategies.
"""

import csv
import random
import threading
import time
import logging
import requests
import socket
from typing import List, Dict, Optional, Iterator
from pathlib import Path
from enum import Enum
from proxy_server_base import UpstreamProxy, ProxyServerConfig


class SelectionMode(Enum):
    """Proxy selection strategies."""
    RANDOM = "random"
    ROUND_ROBIN = "round_robin"
    LOW_LATENCY = "low_latency"


class ProxyPoolManager:
    """Manages the pool of upstream proxies with health checking and selection strategies."""
    
    def __init__(self, config: ProxyServerConfig):
        """Initialize the proxy pool manager."""
        self.config = config
        self.upstream_config = config.get_upstream_config()
        self.logger = logging.getLogger(__name__)
        
        # Proxy pool
        self.proxies: List[UpstreamProxy] = []
        self.healthy_proxies: List[UpstreamProxy] = []
        self.failed_proxies: List[UpstreamProxy] = []
        
        # Selection strategy
        self.selection_mode = SelectionMode(self.upstream_config.get('selection_mode', 'round_robin'))
        self.round_robin_index = 0
        
        # Configuration
        self.csv_file = Path(self.upstream_config.get('csv_file', 'russian_proxies.csv'))
        self.health_check_interval = self.upstream_config.get('health_check_interval', 300)
        self.health_check_timeout = self.upstream_config.get('health_check_timeout', 10)
        self.max_failures = self.upstream_config.get('max_failures', 3)
        self.retry_failed_after = self.upstream_config.get('retry_failed_after', 600)
        self.low_latency_threshold = self.upstream_config.get('low_latency_threshold', 2.0)
        self.auto_reload = self.upstream_config.get('auto_reload', True)
        self.reload_interval = self.upstream_config.get('reload_interval', 3600)
        
        # Threading
        self.lock = threading.RLock()
        self.health_check_thread: Optional[threading.Thread] = None
        self.reload_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        
        # Statistics
        self.stats = {
            'total_proxies': 0,
            'healthy_proxies': 0,
            'failed_proxies': 0,
            'last_health_check': None,
            'last_reload': None,
            'total_requests': 0,
            'successful_requests': 0
        }
        
        # Load initial proxy list
        self.load_proxies()
        
        # Start background threads
        self.start_background_tasks()
    
    def load_proxies(self) -> None:
        """Load proxies from CSV file."""
        if not self.csv_file.exists():
            self.logger.warning(f"Proxy CSV file not found: {self.csv_file}")
            return
        
        new_proxies = []
        
        try:
            with open(self.csv_file, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                for row_num, row in enumerate(reader, start=2):
                    try:
                        # Parse proxy data
                        ip = row.get('ip', '').strip()
                        port_str = row.get('port', '').strip()
                        proxy_type = row.get('type', 'HTTP').strip().upper()
                        status = row.get('status', 'Unknown').strip()
                        
                        if not ip or not port_str:
                            continue
                        
                        port = int(port_str)
                        
                        # Only load valid proxies
                        if status.lower() == 'valid':
                            proxy = UpstreamProxy(
                                ip=ip,
                                port=port,
                                protocol=proxy_type,
                                anonymity=row.get('anonymity', 'Unknown').strip(),
                                response_time=self._parse_float(row.get('response_time')),
                                last_tested=row.get('last_tested', '').strip() or None,
                                status=status,
                                source=row.get('source', '').strip()
                            )
                            new_proxies.append(proxy)
                            
                    except Exception as e:
                        self.logger.debug(f"Skipping invalid proxy row {row_num}: {e}")
                        continue
            
            with self.lock:
                # Preserve existing statistics for proxies that are still in the list
                existing_proxies = {f"{p.ip}:{p.port}": p for p in self.proxies}
                
                for proxy in new_proxies:
                    proxy_key = f"{proxy.ip}:{proxy.port}"
                    if proxy_key in existing_proxies:
                        # Preserve statistics
                        existing = existing_proxies[proxy_key]
                        proxy.failures = existing.failures
                        proxy.last_failure = existing.last_failure
                        proxy.success_count = existing.success_count
                        proxy.total_requests = existing.total_requests
                
                self.proxies = new_proxies
                self._update_proxy_lists()
            
            self.stats['last_reload'] = time.time()
            self.logger.info(f"Loaded {len(new_proxies)} valid proxies from {self.csv_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to load proxies from CSV: {e}")
    
    def _parse_float(self, value: str) -> Optional[float]:
        """Parse float value safely."""
        if not value or not value.strip():
            return None
        try:
            return float(value.strip())
        except ValueError:
            return None
    
    def _update_proxy_lists(self) -> None:
        """Update healthy and failed proxy lists."""
        self.healthy_proxies = []
        self.failed_proxies = []
        
        current_time = time.time()
        
        for proxy in self.proxies:
            # Check if failed proxy should be retried
            if (proxy.failures >= self.max_failures and 
                proxy.last_failure and 
                current_time - proxy.last_failure > self.retry_failed_after):
                proxy.failures = 0  # Reset failures for retry
            
            if proxy.is_healthy:
                self.healthy_proxies.append(proxy)
            else:
                self.failed_proxies.append(proxy)
        
        # Sort healthy proxies by latency for low-latency mode
        if self.selection_mode == SelectionMode.LOW_LATENCY:
            self.healthy_proxies.sort(key=lambda p: p.response_time or float('inf'))
        
        # Update statistics
        self.stats.update({
            'total_proxies': len(self.proxies),
            'healthy_proxies': len(self.healthy_proxies),
            'failed_proxies': len(self.failed_proxies)
        })
    
    def get_proxy(self) -> Optional[UpstreamProxy]:
        """Get next proxy based on selection strategy."""
        with self.lock:
            if not self.healthy_proxies:
                self.logger.warning("No healthy proxies available")
                return None
            
            if self.selection_mode == SelectionMode.RANDOM:
                return random.choice(self.healthy_proxies)
            
            elif self.selection_mode == SelectionMode.ROUND_ROBIN:
                proxy = self.healthy_proxies[self.round_robin_index % len(self.healthy_proxies)]
                self.round_robin_index = (self.round_robin_index + 1) % len(self.healthy_proxies)
                return proxy
            
            elif self.selection_mode == SelectionMode.LOW_LATENCY:
                # Return proxies with response time below threshold, or best available
                low_latency_proxies = [
                    p for p in self.healthy_proxies 
                    if p.response_time and p.response_time <= self.low_latency_threshold
                ]
                
                if low_latency_proxies:
                    return random.choice(low_latency_proxies)
                else:
                    # Return the fastest proxy if none meet threshold
                    return self.healthy_proxies[0] if self.healthy_proxies else None
            
            return None
    
    def record_proxy_success(self, proxy: UpstreamProxy) -> None:
        """Record successful proxy usage."""
        with self.lock:
            proxy.record_success()
            self.stats['successful_requests'] += 1
            self.stats['total_requests'] += 1
            
            # Move proxy back to healthy list if it was failed
            if proxy in self.failed_proxies:
                self.failed_proxies.remove(proxy)
                self.healthy_proxies.append(proxy)
                self._update_proxy_lists()
    
    def record_proxy_failure(self, proxy: UpstreamProxy) -> None:
        """Record proxy failure."""
        with self.lock:
            proxy.record_failure()
            self.stats['total_requests'] += 1
            
            # Move to failed list if it exceeds failure threshold
            if proxy.failures >= self.max_failures and proxy in self.healthy_proxies:
                self.healthy_proxies.remove(proxy)
                self.failed_proxies.append(proxy)
                self.logger.warning(f"Proxy {proxy} marked as failed after {proxy.failures} failures")
    
    def health_check_proxy(self, proxy: UpstreamProxy) -> bool:
        """Perform health check on a single proxy."""
        test_url = "http://httpbin.org/ip"
        
        try:
            proxy_url = f"http://{proxy.ip}:{proxy.port}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxies,
                timeout=self.health_check_timeout,
                verify=False
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                proxy.response_time = response_time
                proxy.status = "Valid"
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.debug(f"Health check failed for {proxy}: {e}")
            return False
    
    def run_health_checks(self) -> None:
        """Run health checks on all proxies."""
        self.logger.info("Starting health check cycle")
        
        with self.lock:
            proxies_to_check = self.proxies.copy()
        
        healthy_count = 0
        failed_count = 0
        
        for proxy in proxies_to_check:
            if self.shutdown_event.is_set():
                break
            
            if self.health_check_proxy(proxy):
                if proxy.failures > 0:
                    proxy.failures = 0  # Reset failures on successful health check
                healthy_count += 1
            else:
                proxy.record_failure()
                failed_count += 1
        
        with self.lock:
            self._update_proxy_lists()
        
        self.stats['last_health_check'] = time.time()
        self.logger.info(f"Health check complete: {healthy_count} healthy, {failed_count} failed")
    
    def start_background_tasks(self) -> None:
        """Start background health checking and reload threads."""
        # Health check thread
        if self.health_check_interval > 0:
            self.health_check_thread = threading.Thread(
                target=self._health_check_worker,
                daemon=True,
                name="HealthCheckWorker"
            )
            self.health_check_thread.start()
        
        # Auto-reload thread
        if self.auto_reload and self.reload_interval > 0:
            self.reload_thread = threading.Thread(
                target=self._reload_worker,
                daemon=True,
                name="ReloadWorker"
            )
            self.reload_thread.start()
    
    def _health_check_worker(self) -> None:
        """Background worker for health checks."""
        while not self.shutdown_event.is_set():
            try:
                self.run_health_checks()
            except Exception as e:
                self.logger.error(f"Health check worker error: {e}")
            
            # Wait for next health check
            self.shutdown_event.wait(self.health_check_interval)
    
    def _reload_worker(self) -> None:
        """Background worker for proxy list reloading."""
        while not self.shutdown_event.is_set():
            # Wait for reload interval
            self.shutdown_event.wait(self.reload_interval)
            
            if not self.shutdown_event.is_set():
                try:
                    self.load_proxies()
                except Exception as e:
                    self.logger.error(f"Reload worker error: {e}")
    
    def reload_proxies(self) -> None:
        """Manually reload proxy list."""
        self.load_proxies()
    
    def get_statistics(self) -> Dict:
        """Get proxy pool statistics."""
        with self.lock:
            stats = self.stats.copy()
            
            # Add per-proxy statistics
            proxy_stats = []
            for proxy in self.proxies:
                proxy_stats.append(proxy.to_dict())
            
            stats['proxies'] = proxy_stats
            stats['selection_mode'] = self.selection_mode.value
            
            return stats
    
    def shutdown(self) -> None:
        """Shutdown the proxy pool manager."""
        self.logger.info("Shutting down proxy pool manager")
        self.shutdown_event.set()
        
        # Wait for threads to finish
        if self.health_check_thread and self.health_check_thread.is_alive():
            self.health_check_thread.join(timeout=5)
        
        if self.reload_thread and self.reload_thread.is_alive():
            self.reload_thread.join(timeout=5)
