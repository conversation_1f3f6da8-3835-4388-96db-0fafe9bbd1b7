{"proxy_sources": {"proxynova": {"name": "ProxyNova", "url": "https://www.proxynova.com/proxy-server-list/country-ru", "method": "html_scraping", "enabled": true, "rate_limit": 2.0, "timeout": 30, "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}, "selectors": {"proxy_table": "table", "proxy_rows": "tr", "ip_column": 0, "port_column": 1, "country_column": 5, "anonymity_column": 6}}, "spys_one": {"name": "Spys.One", "url": "https://spys.one/free-proxy-list/RU/", "method": "html_scraping", "enabled": true, "rate_limit": 2.0, "timeout": 30, "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}, "selectors": {"proxy_table": "table", "proxy_rows": "tr", "ip_port_column": 0, "type_column": 1, "anonymity_column": 2, "country_column": 3}}, "free_proxy_list": {"name": "Free Proxy List", "url": "https://free-proxy-list.net/", "method": "html_scraping", "enabled": true, "rate_limit": 2.0, "timeout": 30, "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}, "selectors": {"proxy_table": "#proxylisttable", "proxy_rows": "tbody tr", "ip_column": 0, "port_column": 1, "country_column": 3, "anonymity_column": 4, "https_column": 6}, "country_filter": "RU"}, "hidemy_name": {"name": "HideMy.name", "url": "https://hidemy.name/en/proxy-list/?country=RU&maxtime=1000&type=hs#list", "method": "html_scraping", "enabled": true, "rate_limit": 3.0, "timeout": 30, "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}, "selectors": {"proxy_table": ".proxy__t", "proxy_rows": "tbody tr", "ip_column": 0, "port_column": 1, "country_column": 2, "speed_column": 3, "type_column": 4, "anonymity_column": 5}}, "geonode": {"name": "GeoNode", "url": "https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc&country=RU", "method": "api", "enabled": true, "rate_limit": 1.0, "timeout": 30, "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}}, "validation": {"test_urls": ["http://httpbin.org/ip", "https://httpbin.org/ip", "http://icanhazip.com", "https://api.ipify.org?format=json"], "timeout": 10, "max_retries": 2, "concurrent_tests": 50, "success_threshold": 0.5, "anonymity_test_url": "http://httpbin.org/headers"}, "output": {"csv_file": "russian_proxies.csv", "csv_columns": ["ip", "port", "type", "country", "anonymity", "response_time", "last_tested", "status", "source", "uptime", "speed"], "backup_enabled": true, "max_backups": 5}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "proxy_collector.log", "max_size": "10MB", "backup_count": 3}, "general": {"max_workers": 10, "request_delay": 1.0, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"]}}