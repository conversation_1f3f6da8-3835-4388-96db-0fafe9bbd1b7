"""
Base Scraper Class for Russian Proxy Collector
Abstract base class that all proxy source scrapers inherit from.
"""

import time
import random
import logging
import requests
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
import re


@dataclass
class ProxyInfo:
    """Data class representing a proxy server."""
    ip: str
    port: int
    type: str = "HTTP"
    country: str = "RU"
    anonymity: str = "Unknown"
    response_time: Optional[float] = None
    last_tested: Optional[str] = None
    status: str = "Unknown"
    source: str = ""
    uptime: Optional[float] = None
    speed: Optional[int] = None
    
    def __post_init__(self):
        """Validate and normalize proxy data after initialization."""
        # Validate IP address format
        if not self._is_valid_ip(self.ip):
            raise ValueError(f"Invalid IP address: {self.ip}")
        
        # Validate port range
        if not (1 <= self.port <= 65535):
            raise ValueError(f"Invalid port number: {self.port}")
        
        # Normalize proxy type
        self.type = self.type.upper()
        if self.type not in ["HTTP", "HTTPS", "SOCKS4", "SOCKS5"]:
            self.type = "HTTP"
        
        # Normalize anonymity level
        anonymity_map = {
            "elite": "Elite",
            "high": "Elite",
            "anonymous": "Anonymous",
            "transparent": "Transparent",
            "noa": "Transparent",
            "hia": "Elite",
            "anm": "Anonymous"
        }
        self.anonymity = anonymity_map.get(self.anonymity.lower(), self.anonymity)
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format."""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert proxy info to dictionary."""
        return {
            'ip': self.ip,
            'port': self.port,
            'type': self.type,
            'country': self.country,
            'anonymity': self.anonymity,
            'response_time': self.response_time,
            'last_tested': self.last_tested,
            'status': self.status,
            'source': self.source,
            'uptime': self.uptime,
            'speed': self.speed
        }
    
    def __str__(self) -> str:
        """String representation of proxy."""
        return f"{self.ip}:{self.port} ({self.type}, {self.anonymity})"


class BaseScraper(ABC):
    """Abstract base class for proxy scrapers."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialize the base scraper.
        
        Args:
            name: Name of the scraper
            config: Configuration dictionary for this scraper
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self.session = requests.Session()
        self._setup_session()
        
        # Rate limiting
        self.last_request_time = 0
        self.rate_limit = config.get('rate_limit', 1.0)
        
        # Statistics
        self.stats = {
            'requests_made': 0,
            'proxies_found': 0,
            'errors': 0,
            'last_run': None
        }
    
    def _setup_session(self) -> None:
        """Setup the requests session with headers and timeouts."""
        headers = self.config.get('headers', {})
        if headers:
            self.session.headers.update(headers)
        
        # Set default timeout
        timeout = self.config.get('timeout', 30)
        self.session.timeout = timeout
        
        # Disable SSL verification warnings for problematic sites
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    def _rate_limit_wait(self) -> None:
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit:
            sleep_time = self.rate_limit - time_since_last
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, url: str, **kwargs) -> requests.Response:
        """
        Make a rate-limited HTTP request.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            requests.RequestException: If request fails
        """
        self._rate_limit_wait()
        
        # Add some randomization to avoid detection
        if 'headers' not in kwargs:
            kwargs['headers'] = {}
        
        # Randomize User-Agent if not specified
        if 'User-Agent' not in kwargs['headers'] and 'user_agents' in self.config:
            user_agents = self.config['user_agents']
            if user_agents:
                kwargs['headers']['User-Agent'] = random.choice(user_agents)
        
        try:
            self.logger.debug(f"Making request to: {url}")
            response = self.session.get(url, verify=False, **kwargs)
            response.raise_for_status()
            self.stats['requests_made'] += 1
            return response
            
        except requests.RequestException as e:
            self.stats['errors'] += 1
            self.logger.error(f"Request failed for {url}: {e}")
            raise
    
    def _parse_proxy_string(self, proxy_str: str) -> Tuple[str, int]:
        """
        Parse proxy string in format 'ip:port' or just 'ip'.
        
        Args:
            proxy_str: Proxy string to parse
            
        Returns:
            Tuple of (ip, port)
            
        Raises:
            ValueError: If proxy string is invalid
        """
        proxy_str = proxy_str.strip()
        
        if ':' in proxy_str:
            ip, port_str = proxy_str.split(':', 1)
            try:
                port = int(port_str)
            except ValueError:
                raise ValueError(f"Invalid port in proxy string: {proxy_str}")
        else:
            ip = proxy_str
            port = 80  # Default HTTP port
        
        return ip.strip(), port
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common HTML entities
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        
        return text
    
    @abstractmethod
    def scrape_proxies(self) -> List[ProxyInfo]:
        """
        Scrape proxies from the source.
        
        Returns:
            List of ProxyInfo objects
            
        Raises:
            Exception: If scraping fails
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get scraper statistics."""
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Reset scraper statistics."""
        self.stats = {
            'requests_made': 0,
            'proxies_found': 0,
            'errors': 0,
            'last_run': None
        }
    
    def __str__(self) -> str:
        """String representation of scraper."""
        return f"{self.__class__.__name__}(name={self.name}, enabled={self.config.get('enabled', False)})"
