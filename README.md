# Russian Proxy Collector

A robust Python script for collecting and validating Russian proxies from multiple sources with comprehensive error handling, logging, and data management capabilities.

## Features

- **Multi-source proxy collection** from 5+ different proxy websites
- **Concurrent proxy validation** with configurable test parameters
- **Modular architecture** with easily extensible scraper classes
- **Robust error handling** with graceful fallbacks and detailed logging
- **CSV data management** with duplicate removal and backup functionality
- **Command-line interface** with flexible options
- **Configuration-driven** with JSON-based settings
- **Production-ready** with proper logging and statistics

## Supported Proxy Sources

1. **ProxyNova.com** - Russian proxy list with anonymity levels
2. **Spys.One** - Comprehensive proxy database with detailed information
3. **Free-Proxy-List.net** - Popular free proxy service
4. **GeoNode API** - High-quality proxy API with detailed metadata
5. **HideMy.name** - Proxy service with speed and anonymity ratings

## Installation

1. **Clone or download the project files**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Verify configuration file exists:**
   ```bash
   ls config.json
   ```

## Quick Start

### Basic Usage

```bash
# Collect and validate proxies from all sources
python russian_proxy_collector.py --collect --validate

# Collect from specific sources only
python russian_proxy_collector.py --collect --sources proxynova,spys_one --validate

# Validate existing proxies
python russian_proxy_collector.py --validate-only --input existing_proxies.csv

# Collect without validation (faster)
python russian_proxy_collector.py --collect-only --output raw_proxies.csv
```

### Advanced Usage

```bash
# Use custom configuration file
python russian_proxy_collector.py --collect --validate --config my_config.json

# Append to existing CSV file
python russian_proxy_collector.py --collect --validate --append

# Verbose output with statistics
python russian_proxy_collector.py --collect --validate --verbose

# Custom output file
python russian_proxy_collector.py --collect --validate --output my_proxies.csv
```

## Configuration

The `config.json` file contains all configuration settings:

### Proxy Sources Configuration
```json
{
  "proxy_sources": {
    "proxynova": {
      "name": "ProxyNova",
      "url": "https://www.proxynova.com/proxy-server-list/country-ru",
      "enabled": true,
      "rate_limit": 2.0,
      "timeout": 30
    }
  }
}
```

### Validation Settings
```json
{
  "validation": {
    "test_urls": [
      "http://httpbin.org/ip",
      "https://httpbin.org/ip"
    ],
    "timeout": 10,
    "max_retries": 2,
    "concurrent_tests": 50,
    "success_threshold": 0.5
  }
}
```

### Output Configuration
```json
{
  "output": {
    "csv_file": "russian_proxies.csv",
    "csv_columns": [
      "ip", "port", "type", "country", "anonymity",
      "response_time", "last_tested", "status", "source"
    ],
    "backup_enabled": true,
    "max_backups": 5
  }
}
```

## Command Line Options

### Actions (Required - choose one)
- `--collect` - Collect proxies from sources (can combine with --validate)
- `--collect-only` - Collect proxies without validation
- `--validate-only` - Validate existing proxies from input file

### Optional Arguments
- `--validate` - Validate collected proxies (used with --collect)
- `--sources SOURCE1,SOURCE2` - Comma-separated list of sources
- `--output FILE` - Output CSV file path
- `--input FILE` - Input CSV file path for validation
- `--config FILE` - Configuration file path (default: config.json)
- `--append` - Append to existing CSV instead of overwriting
- `--verbose` - Enable verbose logging and show statistics

## Output Format

The script outputs proxy data in CSV format with the following columns:

| Column | Description |
|--------|-------------|
| ip | Proxy IP address |
| port | Proxy port number |
| type | Proxy type (HTTP, HTTPS, SOCKS4, SOCKS5) |
| country | Country code (RU for Russian proxies) |
| anonymity | Anonymity level (Elite, Anonymous, Transparent) |
| response_time | Average response time in seconds |
| last_tested | ISO timestamp of last validation |
| status | Validation status (Valid, Invalid, Unknown) |
| source | Source website name |
| uptime | Uptime percentage (if available) |
| speed | Speed rating (if available) |

## Architecture

### Core Components

1. **ConfigManager** - Handles configuration loading and validation
2. **BaseScraper** - Abstract base class for all proxy scrapers
3. **ProxyValidator** - Concurrent proxy validation with multiple test URLs
4. **CSVManager** - CSV data import/export with duplicate handling
5. **Individual Scrapers** - Source-specific scraping implementations

### Modular Design

The system is designed for easy extension:

```python
# Adding a new proxy source
class NewSourceScraper(BaseScraper):
    def scrape_proxies(self) -> List[ProxyInfo]:
        # Implement scraping logic
        pass
```

## Error Handling

The script includes comprehensive error handling:

- **Network failures** - Automatic retries with exponential backoff
- **Site structure changes** - Graceful degradation with detailed logging
- **Rate limiting** - Configurable delays between requests
- **Invalid data** - Data validation with error reporting
- **File operations** - Backup creation and recovery mechanisms

## Logging

Detailed logging is provided at multiple levels:

- **DEBUG** - Detailed execution information
- **INFO** - General operation status
- **WARNING** - Non-fatal issues and fallbacks
- **ERROR** - Serious errors that prevent operation

Log files are automatically rotated and managed according to configuration.

## Performance

- **Concurrent validation** - Tests multiple proxies simultaneously
- **Rate limiting** - Respects website rate limits to avoid blocking
- **Efficient parsing** - Optimized HTML parsing and data extraction
- **Memory management** - Processes large proxy lists efficiently

## Extending the System

### Adding New Proxy Sources

1. Create a new scraper class inheriting from `BaseScraper`
2. Implement the `scrape_proxies()` method
3. Add source configuration to `config.json`
4. Register the scraper in the main application

### Customizing Validation

1. Modify test URLs in configuration
2. Adjust timeout and retry settings
3. Implement custom anonymity testing logic
4. Add new validation criteria

## Troubleshooting

### Common Issues

1. **No proxies collected**
   - Check internet connection
   - Verify source websites are accessible
   - Review rate limiting settings

2. **Validation failures**
   - Increase timeout values
   - Reduce concurrent test count
   - Check test URLs accessibility

3. **Configuration errors**
   - Validate JSON syntax
   - Check required configuration sections
   - Verify file paths and permissions

### Debug Mode

Run with `--verbose` flag for detailed debugging information:

```bash
python russian_proxy_collector.py --collect --validate --verbose
```

## License

This project is provided as-is for educational and research purposes. Please respect the terms of service of the proxy sources and use responsibly.

## Contributing

To contribute to this project:

1. Follow the existing code structure and patterns
2. Add comprehensive error handling
3. Include logging for debugging
4. Update documentation for new features
5. Test with multiple proxy sources

## Disclaimer

This tool is for educational and research purposes only. Users are responsible for complying with the terms of service of proxy sources and applicable laws. The authors are not responsible for any misuse of this software.
