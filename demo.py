#!/usr/bin/env python3
"""
Russian Proxy Collector - Demonstration Script
Shows the capabilities of the proxy collection and validation system.
"""

import os
import sys
from pathlib import Path

def run_demo():
    """Run demonstration of the Russian Proxy Collector."""
    print("🇷🇺 Russian Proxy Collector - Demonstration")
    print("=" * 60)
    
    # Check if required files exist
    required_files = ['config.json', 'russian_proxy_collector.py']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return 1
    
    print("✅ All required files found")
    print()
    
    # Demo 1: Show help
    print("📖 Demo 1: Command Line Help")
    print("-" * 30)
    os.system("python russian_proxy_collector.py --help")
    print()
    
    # Demo 2: Quick collection from GeoNode API (fastest source)
    print("🌐 Demo 2: Quick Proxy Collection from GeoNode API")
    print("-" * 50)
    print("Collecting Russian proxies from GeoNode API...")
    os.system("python russian_proxy_collector.py --collect-only --sources geonode --output demo_proxies.csv")
    
    # Check if file was created
    if Path("demo_proxies.csv").exists():
        print("\n✅ Proxies collected successfully!")
        
        # Show first few lines of the CSV
        print("\n📄 Sample of collected proxies:")
        print("-" * 30)
        with open("demo_proxies.csv", 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:6]):  # Show header + 5 proxies
                print(f"{i+1:2d}: {line.strip()}")
            if len(lines) > 6:
                print(f"... and {len(lines)-6} more proxies")
    else:
        print("❌ Failed to collect proxies")
    
    print()
    
    # Demo 3: Show configuration
    print("⚙️  Demo 3: Configuration Overview")
    print("-" * 35)
    try:
        from config_manager import ConfigManager
        config = ConfigManager()
        sources = config.get_enabled_sources()
        
        print(f"📊 Configured proxy sources: {len(sources)}")
        for name, source_config in sources.items():
            status = "✅ Enabled" if source_config.get('enabled') else "❌ Disabled"
            print(f"   • {source_config.get('name', name)}: {status}")
        
        validation_config = config.get_validation_config()
        print(f"\n🔍 Validation settings:")
        print(f"   • Test URLs: {len(validation_config.get('test_urls', []))}")
        print(f"   • Timeout: {validation_config.get('timeout', 'N/A')}s")
        print(f"   • Concurrent tests: {validation_config.get('concurrent_tests', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
    
    print()
    
    # Demo 4: System test results
    print("🧪 Demo 4: System Test Results")
    print("-" * 30)
    try:
        os.system("python test_system.py")
    except Exception as e:
        print(f"❌ Error running system tests: {e}")
    
    print()
    
    # Demo 5: Available features
    print("🚀 Demo 5: Available Features")
    print("-" * 30)
    features = [
        "✅ Multi-source proxy collection (5 sources)",
        "✅ Concurrent proxy validation",
        "✅ CSV data management with backup",
        "✅ Duplicate removal and data merging",
        "✅ Configurable rate limiting",
        "✅ Comprehensive error handling",
        "✅ Detailed logging and statistics",
        "✅ Command-line interface",
        "✅ Modular and extensible architecture",
        "✅ Production-ready with proper timeouts"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print()
    
    # Usage examples
    print("💡 Usage Examples")
    print("-" * 17)
    examples = [
        "# Collect and validate from all sources:",
        "python russian_proxy_collector.py --collect --validate",
        "",
        "# Collect from specific sources only:",
        "python russian_proxy_collector.py --collect --sources proxynova,geonode",
        "",
        "# Validate existing proxies:",
        "python russian_proxy_collector.py --validate-only --input existing.csv",
        "",
        "# Quick collection without validation:",
        "python russian_proxy_collector.py --collect-only --output quick.csv",
        "",
        "# Verbose mode with statistics:",
        "python russian_proxy_collector.py --collect --validate --verbose"
    ]
    
    for example in examples:
        if example.startswith("#"):
            print(f"\n{example}")
        elif example.startswith("python"):
            print(f"   {example}")
        else:
            print(example)
    
    print()
    print("🎉 Demonstration complete!")
    print("📚 See README.md for detailed documentation")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(run_demo())
