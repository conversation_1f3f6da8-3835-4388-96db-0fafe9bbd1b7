#!/usr/bin/env python3
"""
Test Suite for Russian Proxy Server
Comprehensive testing of proxy server functionality.
"""

import sys
import time
import socket
import threading
import requests
import base64
from pathlib import Path
from typing import Optional, Tuple


def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from proxy_server_base import ProxyServerConfig, AuthenticationManager, UpstreamProxy
        print("✓ Base modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import base modules: {e}")
        return False
    
    try:
        from proxy_pool_manager import ProxyPoolManager
        print("✓ ProxyPoolManager imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ProxyPoolManager: {e}")
        return False
    
    try:
        from http_proxy_handler import HTTPProxyHandler
        print("✓ HTTPProxyHandler imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import HTTPProxyHandler: {e}")
        return False
    
    try:
        from socks5_proxy_handler import SOCKS5ProxyHandler
        print("✓ SOCKS5ProxyHandler imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import SOCKS5ProxyHandler: {e}")
        return False
    
    try:
        from proxy_server import ProxyServer
        print("✓ ProxyServer imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ProxyServer: {e}")
        return False
    
    return True


def test_config():
    """Test configuration loading and validation."""
    print("\nTesting configuration...")
    
    try:
        from proxy_server_base import ProxyServerConfig
        
        # Check if config file exists
        if not Path("proxy_server_config.json").exists():
            print("✗ proxy_server_config.json file not found")
            return False
        
        # Load configuration
        config = ProxyServerConfig("proxy_server_config.json")
        print("✓ Configuration loaded successfully")
        
        # Test basic configuration access
        server_config = config.get_server_config()
        print(f"✓ Server config: HTTP port {server_config.get('http_port')}, SOCKS port {server_config.get('socks_port')}")
        
        auth_config = config.get_auth_config()
        print(f"✓ Authentication: {'enabled' if auth_config.get('enabled') else 'disabled'}")
        
        upstream_config = config.get_upstream_config()
        print(f"✓ Upstream config: CSV file {upstream_config.get('csv_file')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_upstream_proxy():
    """Test UpstreamProxy data class."""
    print("\nTesting UpstreamProxy...")
    
    try:
        from proxy_server_base import UpstreamProxy
        
        # Test valid proxy creation
        proxy = UpstreamProxy(
            ip="***********",
            port=8080,
            protocol="HTTP",
            anonymity="Elite",
            status="Valid"
        )
        print("✓ UpstreamProxy object created successfully")
        
        # Test health check
        assert proxy.is_healthy == True
        print("✓ Health check works correctly")
        
        # Test statistics
        proxy.record_success()
        assert proxy.success_count == 1
        assert proxy.total_requests == 1
        print("✓ Success recording works")
        
        proxy.record_failure()
        assert proxy.failures == 1
        assert proxy.total_requests == 2
        print("✓ Failure recording works")
        
        # Test dictionary conversion
        proxy_dict = proxy.to_dict()
        assert 'ip' in proxy_dict
        assert 'port' in proxy_dict
        print("✓ Dictionary conversion works")
        
        return True
        
    except Exception as e:
        print(f"✗ UpstreamProxy test failed: {e}")
        return False


def test_authentication():
    """Test authentication manager."""
    print("\nTesting authentication...")
    
    try:
        from proxy_server_base import ProxyServerConfig, AuthenticationManager
        
        config = ProxyServerConfig("proxy_server_config.json")
        auth_manager = AuthenticationManager(config)
        print("✓ AuthenticationManager created successfully")
        
        # Test credential validation
        username = auth_manager.username
        password = auth_manager.password
        
        assert auth_manager.validate_credentials(username, password) == True
        print("✓ Valid credentials accepted")
        
        assert auth_manager.validate_credentials("wrong", "wrong") == False
        print("✓ Invalid credentials rejected")
        
        # Test HTTP auth header
        auth_string = f"{username}:{password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        auth_header = f"Basic {encoded_auth}"
        
        assert auth_manager.validate_http_auth(auth_header) == True
        print("✓ HTTP Basic auth validation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Authentication test failed: {e}")
        return False


def test_proxy_pool():
    """Test proxy pool manager."""
    print("\nTesting proxy pool manager...")
    
    try:
        from proxy_server_base import ProxyServerConfig
        from proxy_pool_manager import ProxyPoolManager
        
        config = ProxyServerConfig("proxy_server_config.json")
        pool_manager = ProxyPoolManager(config)
        print("✓ ProxyPoolManager created successfully")
        
        # Test statistics
        stats = pool_manager.get_statistics()
        print(f"✓ Pool statistics: {stats['total_proxies']} total, {stats['healthy_proxies']} healthy")
        
        # Test proxy selection
        if stats['healthy_proxies'] > 0:
            proxy = pool_manager.get_proxy()
            if proxy:
                print(f"✓ Proxy selection works: {proxy}")
            else:
                print("⚠ No proxy returned (may be normal if no healthy proxies)")
        else:
            print("⚠ No healthy proxies available for testing")
        
        return True
        
    except Exception as e:
        print(f"✗ Proxy pool test failed: {e}")
        return False


def test_server_initialization():
    """Test proxy server initialization."""
    print("\nTesting server initialization...")
    
    try:
        from proxy_server import ProxyServer
        
        # Test server creation
        server = ProxyServer("proxy_server_config.json")
        print("✓ ProxyServer created successfully")
        
        # Test components
        assert server.auth_manager is not None
        print("✓ Authentication manager initialized")
        
        assert server.pool_manager is not None
        print("✓ Pool manager initialized")
        
        assert server.http_handler is not None
        print("✓ HTTP handler initialized")
        
        assert server.socks5_handler is not None
        print("✓ SOCKS5 handler initialized")
        
        # Test configuration access
        stats = server.get_statistics()
        print("✓ Statistics retrieval works")
        
        return True
        
    except Exception as e:
        print(f"✗ Server initialization test failed: {e}")
        return False


def test_port_availability():
    """Test if configured ports are available."""
    print("\nTesting port availability...")
    
    try:
        from proxy_server_base import ProxyServerConfig
        
        config = ProxyServerConfig("proxy_server_config.json")
        server_config = config.get_server_config()
        
        http_port = server_config.get('http_port', 8080)
        socks_port = server_config.get('socks_port', 1080)
        bind_address = server_config.get('bind_address', '127.0.0.1')
        
        # Test HTTP port
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind((bind_address, http_port))
            sock.close()
            print(f"✓ HTTP port {http_port} is available")
        except OSError:
            print(f"⚠ HTTP port {http_port} is in use")
        
        # Test SOCKS port
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind((bind_address, socks_port))
            sock.close()
            print(f"✓ SOCKS5 port {socks_port} is available")
        except OSError:
            print(f"⚠ SOCKS5 port {socks_port} is in use")
        
        return True
        
    except Exception as e:
        print(f"✗ Port availability test failed: {e}")
        return False


def test_http_proxy_connection():
    """Test HTTP proxy connection (requires running server)."""
    print("\nTesting HTTP proxy connection...")
    
    try:
        from proxy_server_base import ProxyServerConfig
        
        config = ProxyServerConfig("proxy_server_config.json")
        server_config = config.get_server_config()
        auth_config = config.get_auth_config()
        
        http_port = server_config.get('http_port', 8080)
        bind_address = server_config.get('bind_address', '127.0.0.1')
        
        proxy_url = f"http://{bind_address}:{http_port}"
        
        # Test if server is running
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((bind_address, http_port))
            sock.close()
            
            if result != 0:
                print("⚠ HTTP proxy server not running - skipping connection test")
                return True
        except:
            print("⚠ Cannot check if HTTP proxy server is running")
            return True
        
        # Prepare authentication if enabled
        proxies = {'http': proxy_url, 'https': proxy_url}
        
        if auth_config.get('enabled', True):
            username = auth_config.get('username', 'user')
            password = auth_config.get('password', 'pass')
            auth_proxy_url = f"http://{username}:{password}@{bind_address}:{http_port}"
            proxies = {'http': auth_proxy_url, 'https': auth_proxy_url}
        
        # Test HTTP request through proxy
        try:
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✓ HTTP proxy connection successful")
                return True
            else:
                print(f"⚠ HTTP proxy returned status {response.status_code}")
                return True
                
        except requests.exceptions.ProxyError:
            print("⚠ HTTP proxy connection failed (may be normal if no upstream proxies)")
            return True
        except requests.exceptions.Timeout:
            print("⚠ HTTP proxy connection timed out")
            return True
        except Exception as e:
            print(f"⚠ HTTP proxy test error: {e}")
            return True
        
    except Exception as e:
        print(f"✗ HTTP proxy connection test failed: {e}")
        return False


def test_monitor():
    """Test proxy monitor functionality."""
    print("\nTesting proxy monitor...")
    
    try:
        from proxy_monitor import ProxyMonitor
        from proxy_server_base import ProxyServerConfig
        
        config = ProxyServerConfig("proxy_server_config.json")
        monitor = ProxyMonitor(config)
        print("✓ ProxyMonitor created successfully")
        
        # Test statistics update
        test_stats = {
            'active_connections': 5,
            'total_connections': 100,
            'uptime': 3600
        }
        
        monitor.update_stats(test_stats)
        current = monitor.get_current_stats()
        assert current['active_connections'] == 5
        print("✓ Statistics update works")
        
        # Test history
        history = monitor.get_history(60)
        assert len(history) >= 1
        print("✓ Statistics history works")
        
        # Test summary
        summary = monitor.get_summary_stats(60)
        print("✓ Summary statistics work")
        
        monitor.shutdown()
        return True
        
    except Exception as e:
        print(f"✗ Monitor test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Russian Proxy Server - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_upstream_proxy,
        test_authentication,
        test_proxy_pool,
        test_server_initialization,
        test_port_availability,
        test_http_proxy_connection,
        test_monitor
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Proxy server is ready to use.")
        return 0
    else:
        print("✗ Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
