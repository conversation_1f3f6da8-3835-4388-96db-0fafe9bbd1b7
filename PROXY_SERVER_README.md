# Russian Proxy Server

A sophisticated companion proxy server that works with the Russian Proxy Collector results to provide local HTTP and SOCKS5 proxy services with authentication, load balancing, and intelligent upstream proxy management.

## Features

### Core Functionality
- **Dual Protocol Support**: Simultaneous HTTP/HTTPS and SOCKS5 proxy protocols
- **Authentication System**: Username/password authentication for both protocols
- **Upstream Proxy Pool**: Intelligent management of collected Russian proxies
- **Load Balancing**: Multiple proxy selection strategies (random, round-robin, low-latency)
- **Protocol Translation**: Seamless forwarding between different proxy protocols
- **Health Checking**: Automatic monitoring and failover of upstream proxies

### Advanced Features
- **Real-time Statistics**: Connection monitoring and performance metrics
- **Hot Reload**: Dynamic proxy list updates without restart
- **Concurrent Connections**: Multi-threaded handling of client connections
- **Comprehensive Logging**: Detailed logging with configurable verbosity
- **Configuration Management**: JSON-based configuration with CLI overrides
- **Monitoring Dashboard**: Real-time status and performance monitoring

## Installation

1. **Ensure you have the Russian Proxy Collector results:**
   ```bash
   # Run the collector first to get proxy list
   python russian_proxy_collector.py --collect --validate --output russian_proxies.csv
   ```

2. **Install additional dependencies:**
   ```bash
   pip install requests
   ```

3. **Verify configuration file exists:**
   ```bash
   ls proxy_server_config.json
   ```

## Quick Start

### Basic Usage

```bash
# Start with default settings
python proxy_server.py

# Custom ports and authentication
python proxy_server.py --http-port 8080 --socks-port 1080 --auth user:pass

# Use specific proxy file and selection mode
python proxy_server.py --proxy-file my_proxies.csv --mode round-robin

# Low-latency mode with verbose logging
python proxy_server.py --low-latency --verbose
```

### Client Configuration

Once the server is running, configure your applications to use:

**HTTP Proxy:**
- Host: `127.0.0.1`
- Port: `8080` (default)
- Authentication: `proxyuser:proxypass` (default)

**SOCKS5 Proxy:**
- Host: `127.0.0.1`
- Port: `1080` (default)
- Authentication: `proxyuser:proxypass` (default)

## Configuration

### Main Configuration File (`proxy_server_config.json`)

```json
{
  "server": {
    "http_port": 8080,
    "socks_port": 1080,
    "bind_address": "127.0.0.1",
    "max_connections": 1000
  },
  "authentication": {
    "enabled": true,
    "username": "proxyuser",
    "password": "proxypass"
  },
  "upstream_proxies": {
    "csv_file": "russian_proxies.csv",
    "selection_mode": "round_robin",
    "health_check_interval": 300,
    "auto_reload": true
  }
}
```

### Proxy Selection Modes

1. **Random** (`random`): Randomly select from healthy proxies
2. **Round Robin** (`round_robin`): Cycle through proxies in order
3. **Low Latency** (`low_latency`): Prioritize fastest responding proxies

## Command Line Options

### Server Control
```bash
python proxy_server.py [OPTIONS]
```

**Configuration:**
- `--config FILE` - Configuration file path
- `--http-port PORT` - HTTP proxy port (overrides config)
- `--socks-port PORT` - SOCKS5 proxy port (overrides config)
- `--bind ADDRESS` - Bind address (overrides config)

**Authentication:**
- `--auth user:pass` - Set authentication credentials
- `--no-auth` - Disable authentication

**Proxy Management:**
- `--proxy-file FILE` - Proxy CSV file path
- `--mode MODE` - Selection mode (random/round-robin/low-latency)
- `--low-latency` - Enable low-latency mode

**Control:**
- `--verbose` - Enable verbose logging
- `--daemon` - Run as daemon process

## Architecture

### Core Components

1. **ProxyServer** - Main server coordinator
2. **HTTPProxyHandler** - HTTP/HTTPS protocol implementation
3. **SOCKS5ProxyHandler** - SOCKS5 protocol implementation
4. **ProxyPoolManager** - Upstream proxy management
5. **AuthenticationManager** - Authentication handling
6. **ProxyMonitor** - Statistics and monitoring

### Protocol Support

**Incoming Protocols:**
- HTTP/HTTPS proxy (CONNECT method and direct requests)
- SOCKS5 with username/password authentication

**Upstream Protocols:**
- HTTP proxies
- HTTPS proxies
- SOCKS4 proxies
- SOCKS5 proxies

### Load Balancing

The server intelligently routes traffic through upstream Russian proxies:

- **Health Monitoring**: Continuous health checks of upstream proxies
- **Automatic Failover**: Failed proxies are automatically removed from rotation
- **Performance Tracking**: Success rates and response times are monitored
- **Smart Selection**: Proxies are selected based on configured strategy

## Monitoring and Statistics

### Real-time Monitoring

```bash
# View live server status
python proxy_monitor.py

# Export statistics
python proxy_monitor.py --export-only
```

### Statistics Include:
- Active connections (HTTP and SOCKS5)
- Total connection counts
- Upstream proxy health and performance
- Success rates and response times
- Bytes transferred
- Server uptime

### Log Files
- `proxy_server.log` - Main server log
- `proxy_stats.json` - Exported statistics

## Integration with Russian Proxy Collector

The proxy server seamlessly integrates with the Russian Proxy Collector:

1. **Automatic Loading**: Reads proxy lists from collector CSV output
2. **Hot Reload**: Automatically reloads proxy list when updated
3. **Health Validation**: Uses collector validation results
4. **Performance Data**: Leverages response time and anonymity information

### Workflow Integration

```bash
# 1. Collect and validate proxies
python russian_proxy_collector.py --collect --validate --output proxies.csv

# 2. Start proxy server with collected proxies
python proxy_server.py --proxy-file proxies.csv --mode low-latency

# 3. Configure applications to use local proxy
# HTTP: 127.0.0.1:8080
# SOCKS5: 127.0.0.1:1080
```

## Security Features

- **Authentication Required**: Username/password protection
- **Connection Limits**: Configurable maximum connections
- **Rate Limiting**: Request rate limiting per IP
- **Domain Filtering**: Optional domain allow/block lists
- **Private IP Blocking**: Option to block private IP ranges

## Performance Optimization

- **Connection Pooling**: Efficient upstream connection reuse
- **Concurrent Processing**: Multi-threaded request handling
- **Smart Caching**: Connection state caching
- **Buffer Optimization**: Configurable buffer sizes
- **Timeout Management**: Intelligent timeout handling

## Troubleshooting

### Common Issues

1. **No upstream proxies available**
   - Run the Russian Proxy Collector first
   - Check CSV file path in configuration
   - Verify proxies are marked as "Valid"

2. **Authentication failures**
   - Check username/password in configuration
   - Verify client authentication settings
   - Review authentication logs

3. **Connection timeouts**
   - Increase timeout values in configuration
   - Check upstream proxy health
   - Verify network connectivity

### Debug Mode

```bash
# Enable verbose logging
python proxy_server.py --verbose

# Check server status
python proxy_monitor.py --interval 5
```

### Testing

```bash
# Run comprehensive tests
python test_proxy_server.py

# Test specific functionality
curl -x ***************************************** http://httpbin.org/ip
```

## Advanced Configuration

### Custom Health Checks
```json
{
  "upstream_proxies": {
    "health_check_interval": 300,
    "health_check_timeout": 10,
    "max_failures": 3,
    "retry_failed_after": 600
  }
}
```

### Performance Tuning
```json
{
  "server": {
    "max_connections": 1000,
    "connection_timeout": 30,
    "buffer_size": 8192
  }
}
```

### Logging Configuration
```json
{
  "logging": {
    "level": "INFO",
    "file": "proxy_server.log",
    "max_size": "50MB",
    "backup_count": 5
  }
}
```

## API and Extensibility

The proxy server is designed for easy extension:

- **Plugin Architecture**: Add custom proxy handlers
- **Protocol Support**: Implement additional proxy protocols
- **Selection Strategies**: Create custom load balancing algorithms
- **Monitoring Extensions**: Add custom metrics and alerts

## License

This project is provided as-is for educational and research purposes. Please respect the terms of service of upstream proxy sources and use responsibly.

## Contributing

To contribute to this project:

1. Follow the existing code structure and patterns
2. Add comprehensive error handling and logging
3. Include tests for new functionality
4. Update documentation for new features
5. Ensure compatibility with the Russian Proxy Collector

## Disclaimer

This tool is for educational and research purposes only. Users are responsible for complying with applicable laws and the terms of service of proxy sources. The authors are not responsible for any misuse of this software.
