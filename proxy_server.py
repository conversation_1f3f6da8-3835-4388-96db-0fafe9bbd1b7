#!/usr/bin/env python3
"""
Russian Proxy Server
A companion proxy server that works with Russian Proxy Collector results.
Provides local HTTP and SOCKS5 proxy services with authentication and load balancing.

Usage:
    python proxy_server.py --http-port 8080 --socks-port 1080 --auth user:pass --proxy-file proxies.csv
    python proxy_server.py --config proxy_server_config.json --mode round-robin --low-latency
"""

import argparse
import signal
import socket
import sys
import threading
import time
import logging
from pathlib import Path
from typing import Optional

# Import our modules
from proxy_server_base import ProxyServerConfig, AuthenticationManager, setup_logging
from proxy_pool_manager import ProxyPoolManager, SelectionMode
from http_proxy_handler import HTTPProxyHandler
from socks5_proxy_handler import SOCKS5ProxyHandler


class ProxyServer:
    """Main proxy server class that coordinates HTTP and SOCKS5 services."""
    
    def __init__(self, config_path: str = "proxy_server_config.json"):
        """Initialize the proxy server."""
        self.config = ProxyServerConfig(config_path)
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.auth_manager = AuthenticationManager(self.config)
        self.pool_manager = ProxyPoolManager(self.config)
        self.http_handler = HTTPProxyHandler(self.config, self.auth_manager, self.pool_manager)
        self.socks5_handler = SOCKS5ProxyHandler(self.config, self.auth_manager, self.pool_manager)
        
        # Server configuration
        server_config = self.config.get_server_config()
        self.bind_address = server_config.get('bind_address', '127.0.0.1')
        self.http_port = server_config.get('http_port', 8080)
        self.socks_port = server_config.get('socks_port', 1080)
        self.max_connections = server_config.get('max_connections', 1000)
        
        # Server sockets
        self.http_socket: Optional[socket.socket] = None
        self.socks_socket: Optional[socket.socket] = None
        
        # Server threads
        self.http_thread: Optional[threading.Thread] = None
        self.socks_thread: Optional[threading.Thread] = None
        self.stats_thread: Optional[threading.Thread] = None
        
        # Control
        self.shutdown_event = threading.Event()
        self.running = False
        
        # Statistics
        self.start_time = time.time()
        self.stats = {
            'start_time': self.start_time,
            'http_connections': 0,
            'socks_connections': 0,
            'total_connections': 0,
            'active_connections': 0,
            'bytes_transferred': 0
        }
    
    def start(self) -> None:
        """Start the proxy server."""
        self.logger.info("Starting Russian Proxy Server")
        
        try:
            # Check if proxy pool has proxies
            pool_stats = self.pool_manager.get_statistics()
            if pool_stats['healthy_proxies'] == 0:
                self.logger.warning("No healthy upstream proxies available!")
            else:
                self.logger.info(f"Loaded {pool_stats['healthy_proxies']} healthy upstream proxies")
            
            # Start HTTP proxy server
            if self.config.get_protocol_config('http').get('enabled', True):
                self._start_http_server()
            
            # Start SOCKS5 proxy server
            if self.config.get_protocol_config('socks5').get('enabled', True):
                self._start_socks5_server()
            
            # Start statistics thread
            if self.config.get_stats_config().get('enabled', True):
                self._start_stats_thread()
            
            self.running = True
            self.logger.info(f"Proxy server started successfully")
            self.logger.info(f"HTTP proxy listening on {self.bind_address}:{self.http_port}")
            self.logger.info(f"SOCKS5 proxy listening on {self.bind_address}:{self.socks_port}")
            
            if self.auth_manager.enabled:
                self.logger.info(f"Authentication enabled (user: {self.auth_manager.username})")
            else:
                self.logger.warning("Authentication disabled - server is open to all connections")
            
        except Exception as e:
            self.logger.error(f"Failed to start proxy server: {e}")
            self.shutdown()
            raise
    
    def _start_http_server(self) -> None:
        """Start HTTP proxy server."""
        self.http_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.http_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.http_socket.bind((self.bind_address, self.http_port))
        self.http_socket.listen(self.max_connections)
        
        self.http_thread = threading.Thread(
            target=self._http_server_worker,
            daemon=True,
            name="HTTPProxyServer"
        )
        self.http_thread.start()
    
    def _start_socks5_server(self) -> None:
        """Start SOCKS5 proxy server."""
        self.socks_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socks_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.socks_socket.bind((self.bind_address, self.socks_port))
        self.socks_socket.listen(self.max_connections)
        
        self.socks_thread = threading.Thread(
            target=self._socks5_server_worker,
            daemon=True,
            name="SOCKS5ProxyServer"
        )
        self.socks_thread.start()
    
    def _start_stats_thread(self) -> None:
        """Start statistics monitoring thread."""
        self.stats_thread = threading.Thread(
            target=self._stats_worker,
            daemon=True,
            name="StatsWorker"
        )
        self.stats_thread.start()
    
    def _http_server_worker(self) -> None:
        """HTTP proxy server worker thread."""
        self.logger.info(f"HTTP proxy server started on {self.bind_address}:{self.http_port}")
        
        while not self.shutdown_event.is_set():
            try:
                self.http_socket.settimeout(1.0)  # Allow periodic shutdown checks
                client_socket, client_address = self.http_socket.accept()
                
                # Update statistics
                self.stats['http_connections'] += 1
                self.stats['total_connections'] += 1
                self.stats['active_connections'] += 1
                
                # Handle connection in separate thread
                connection_thread = threading.Thread(
                    target=self._handle_http_connection,
                    args=(client_socket, client_address),
                    daemon=True
                )
                connection_thread.start()
                
            except socket.timeout:
                continue
            except Exception as e:
                if not self.shutdown_event.is_set():
                    self.logger.error(f"HTTP server error: {e}")
                break
    
    def _socks5_server_worker(self) -> None:
        """SOCKS5 proxy server worker thread."""
        self.logger.info(f"SOCKS5 proxy server started on {self.bind_address}:{self.socks_port}")
        
        while not self.shutdown_event.is_set():
            try:
                self.socks_socket.settimeout(1.0)  # Allow periodic shutdown checks
                client_socket, client_address = self.socks_socket.accept()
                
                # Update statistics
                self.stats['socks_connections'] += 1
                self.stats['total_connections'] += 1
                self.stats['active_connections'] += 1
                
                # Handle connection in separate thread
                connection_thread = threading.Thread(
                    target=self._handle_socks5_connection,
                    args=(client_socket, client_address),
                    daemon=True
                )
                connection_thread.start()
                
            except socket.timeout:
                continue
            except Exception as e:
                if not self.shutdown_event.is_set():
                    self.logger.error(f"SOCKS5 server error: {e}")
                break
    
    def _handle_http_connection(self, client_socket: socket.socket, client_address: tuple) -> None:
        """Handle HTTP proxy connection."""
        try:
            self.http_handler.handle_connection(client_socket, client_address)
        except Exception as e:
            self.logger.debug(f"HTTP connection error from {client_address[0]}: {e}")
        finally:
            self.stats['active_connections'] -= 1
    
    def _handle_socks5_connection(self, client_socket: socket.socket, client_address: tuple) -> None:
        """Handle SOCKS5 proxy connection."""
        try:
            self.socks5_handler.handle_connection(client_socket, client_address)
        except Exception as e:
            self.logger.debug(f"SOCKS5 connection error from {client_address[0]}: {e}")
        finally:
            self.stats['active_connections'] -= 1
    
    def _stats_worker(self) -> None:
        """Statistics monitoring worker thread."""
        stats_config = self.config.get_stats_config()
        update_interval = stats_config.get('update_interval', 5)
        
        while not self.shutdown_event.is_set():
            try:
                self._update_statistics()
                self.shutdown_event.wait(update_interval)
            except Exception as e:
                self.logger.error(f"Stats worker error: {e}")
                break
    
    def _update_statistics(self) -> None:
        """Update server statistics."""
        # Get pool statistics
        pool_stats = self.pool_manager.get_statistics()
        
        # Get handler statistics
        http_connections = self.http_handler.get_active_connections()
        socks_connections = self.socks5_handler.get_active_connections()
        
        # Update combined statistics
        self.stats.update({
            'uptime': time.time() - self.start_time,
            'pool_stats': pool_stats,
            'active_http_connections': len(http_connections),
            'active_socks_connections': len(socks_connections)
        })
        
        # Log periodic statistics
        if self.logger.isEnabledFor(logging.INFO):
            self.logger.info(
                f"Stats: {self.stats['active_connections']} active, "
                f"{pool_stats['healthy_proxies']} healthy proxies, "
                f"{self.stats['total_connections']} total connections"
            )
    
    def get_statistics(self) -> dict:
        """Get comprehensive server statistics."""
        self._update_statistics()
        return self.stats.copy()
    
    def reload_proxies(self) -> None:
        """Reload proxy list from CSV file."""
        self.logger.info("Reloading proxy list")
        self.pool_manager.reload_proxies()
    
    def shutdown(self) -> None:
        """Shutdown the proxy server."""
        if not self.running:
            return
        
        self.logger.info("Shutting down proxy server")
        self.shutdown_event.set()
        self.running = False
        
        # Close server sockets
        if self.http_socket:
            try:
                self.http_socket.close()
            except:
                pass
        
        if self.socks_socket:
            try:
                self.socks_socket.close()
            except:
                pass
        
        # Wait for threads to finish
        threads = [self.http_thread, self.socks_thread, self.stats_thread]
        for thread in threads:
            if thread and thread.is_alive():
                thread.join(timeout=5)
        
        # Shutdown pool manager
        self.pool_manager.shutdown()
        
        self.logger.info("Proxy server shutdown complete")
    
    def wait_for_shutdown(self) -> None:
        """Wait for shutdown signal."""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
            self.shutdown()


def parse_auth_string(auth_str: str) -> tuple:
    """Parse authentication string in format 'username:password'."""
    if ':' not in auth_str:
        raise ValueError("Authentication must be in format 'username:password'")
    
    username, password = auth_str.split(':', 1)
    return username.strip(), password.strip()


def main() -> int:
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Russian Proxy Server - Local proxy service using collected Russian proxies",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start with default settings
  python proxy_server.py
  
  # Custom ports and authentication
  python proxy_server.py --http-port 8080 --socks-port 1080 --auth user:pass
  
  # Use specific proxy file and selection mode
  python proxy_server.py --proxy-file my_proxies.csv --mode round-robin
  
  # Low-latency mode with custom config
  python proxy_server.py --config my_config.json --low-latency
        """
    )
    
    # Configuration
    parser.add_argument(
        '--config',
        type=str,
        default='proxy_server_config.json',
        help='Configuration file path (default: proxy_server_config.json)'
    )
    
    # Server settings
    parser.add_argument(
        '--http-port',
        type=int,
        help='HTTP proxy port (overrides config)'
    )
    parser.add_argument(
        '--socks-port',
        type=int,
        help='SOCKS5 proxy port (overrides config)'
    )
    parser.add_argument(
        '--bind',
        type=str,
        help='Bind address (overrides config)'
    )
    
    # Authentication
    parser.add_argument(
        '--auth',
        type=str,
        help='Authentication in format username:password (overrides config)'
    )
    parser.add_argument(
        '--no-auth',
        action='store_true',
        help='Disable authentication'
    )
    
    # Proxy settings
    parser.add_argument(
        '--proxy-file',
        type=str,
        help='Proxy CSV file path (overrides config)'
    )
    parser.add_argument(
        '--mode',
        choices=['random', 'round-robin', 'low-latency'],
        help='Proxy selection mode (overrides config)'
    )
    parser.add_argument(
        '--low-latency',
        action='store_true',
        help='Enable low-latency mode (same as --mode low-latency)'
    )
    
    # Control
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    parser.add_argument(
        '--daemon',
        action='store_true',
        help='Run as daemon (background process)'
    )
    
    args = parser.parse_args()
    
    # Check if config file exists
    if not Path(args.config).exists():
        print(f"Error: Configuration file '{args.config}' not found")
        return 1
    
    try:
        # Load configuration
        config = ProxyServerConfig(args.config)
        
        # Apply command line overrides
        if args.http_port:
            config.config['server']['http_port'] = args.http_port
        
        if args.socks_port:
            config.config['server']['socks_port'] = args.socks_port
        
        if args.bind:
            config.config['server']['bind_address'] = args.bind
        
        if args.auth:
            username, password = parse_auth_string(args.auth)
            config.config['authentication']['enabled'] = True
            config.config['authentication']['username'] = username
            config.config['authentication']['password'] = password
        
        if args.no_auth:
            config.config['authentication']['enabled'] = False
        
        if args.proxy_file:
            config.config['upstream_proxies']['csv_file'] = args.proxy_file
        
        if args.mode:
            config.config['upstream_proxies']['selection_mode'] = args.mode
        elif args.low_latency:
            config.config['upstream_proxies']['selection_mode'] = 'low_latency'
        
        # Setup logging
        if args.verbose:
            config.config['logging']['level'] = 'DEBUG'
        
        setup_logging(config)
        
        # Create and start server
        server = ProxyServer(args.config)
        
        # Setup signal handlers
        def signal_handler(signum, frame):
            server.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start server
        server.start()
        
        # Wait for shutdown
        server.wait_for_shutdown()
        
        return 0
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        return 0
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
