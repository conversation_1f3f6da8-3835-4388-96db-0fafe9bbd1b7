#!/usr/bin/env python3
"""
Test the validation fix with improved HTTP status code handling.
"""

import requests
import urllib3

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_improved_validation():
    """Test the improved validation logic."""
    print("🔍 Testing improved validation logic...")
    
    # Test the case that previously failed: *******:80 returning HTTP 409
    proxy_url = "http://*******:80"
    proxies = {'http': proxy_url, 'https': proxy_url}
    
    print(f"Testing proxy: {proxy_url}")
    
    try:
        response = requests.get(
            "http://httpbin.org/ip",
            proxies=proxies,
            timeout=10,
            verify=False
        )
        
        print(f"Status code: {response.status_code}")
        
        # Apply the new validation logic
        success_codes = [200, 301, 302, 403, 404]
        is_success = response.status_code in success_codes
        
        print(f"Old logic (only 200): {'✅ SUCCESS' if response.status_code == 200 else '❌ FAILURE'}")
        print(f"New logic (200,301,302,403,404): {'✅ SUCCESS' if is_success else '❌ FAILURE'}")
        
        if is_success:
            print("🎉 The fix works! This proxy would now be considered valid.")
            return True
        else:
            print(f"Still failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Connection error: {str(e)[:100]}")
        
    return False

def test_geonode_with_fix():
    """Test GeoNode proxies with the improved validation."""
    print("\n🔍 Testing GeoNode proxies with improved validation...")
    
    try:
        # Get a few proxies from GeoNode
        api_url = "https://proxylist.geonode.com/api/proxy-list?limit=3&page=1&sort_by=lastChecked&sort_type=desc&country=US"
        response = requests.get(api_url, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ GeoNode API failed: {response.status_code}")
            return 0
            
        data = response.json()
        proxies = data.get('data', [])
        
        if not proxies:
            print("❌ No proxies returned")
            return 0
            
        working_count = 0
        
        for i, proxy_data in enumerate(proxies):
            ip = proxy_data.get('ip')
            port = proxy_data.get('port')
            protocols = proxy_data.get('protocols', [])
            uptime = proxy_data.get('upTime', 0)
            
            print(f"\n--- Testing proxy {i+1}: {ip}:{port} ---")
            print(f"Protocols: {protocols}, Uptime: {uptime}%")
            
            # Test with HTTP if available
            if 'http' in protocols:
                proxy_url = f"http://{ip}:{port}"
            elif 'socks4' in protocols:
                proxy_url = f"socks4://{ip}:{port}"
            elif 'socks5' in protocols:
                proxy_url = f"socks5://{ip}:{port}"
            else:
                proxy_url = f"http://{ip}:{port}"
                
            proxies_dict = {'http': proxy_url, 'https': proxy_url}
            
            try:
                response = requests.get(
                    "http://httpbin.org/ip",
                    proxies=proxies_dict,
                    timeout=15,
                    verify=False
                )
                
                print(f"Status: {response.status_code}")
                
                # Apply improved validation logic
                success_codes = [200, 301, 302, 403, 404]
                old_success = response.status_code == 200
                new_success = response.status_code in success_codes
                
                print(f"Old logic: {'✅' if old_success else '❌'}")
                print(f"New logic: {'✅' if new_success else '❌'}")
                
                if new_success:
                    working_count += 1
                    print("🎉 PROXY WORKS with new logic!")
                    if response.status_code == 200:
                        print(f"Response: {response.text[:100]}")
                        
            except requests.exceptions.Timeout:
                print("❌ Timeout")
            except Exception as e:
                print(f"❌ Error: {str(e)[:100]}")
                
        print(f"\n📊 Results: {working_count}/{len(proxies)} proxies work with improved validation")
        return working_count
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0

def main():
    """Test the validation fix."""
    print("🔧 TESTING VALIDATION FIX")
    print("=" * 50)
    
    # Test the specific case we found
    if test_improved_validation():
        print("\n✅ Fix confirmed working!")
    
    # Test with real GeoNode proxies
    working_count = test_geonode_with_fix()
    
    if working_count > 0:
        print(f"\n🎉 SUCCESS! Found {working_count} working proxies with the fix!")
        print("The bug was indeed the overly strict validation logic.")
    else:
        print("\n🤔 Still no working proxies found. May need further investigation.")

if __name__ == "__main__":
    main()
