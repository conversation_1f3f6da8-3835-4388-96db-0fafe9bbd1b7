#!/usr/bin/env python3
"""
Debug script to investigate proxy validation failures.
Tests individual proxy validation to identify root cause.
"""

import requests
import time
import json
from typing import Dict, Any, List
import urllib3

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_direct_connection():
    """Test direct connection to validation URLs."""
    test_urls = [
        "http://httpbin.org/ip",
        "https://httpbin.org/ip", 
        "http://icanhazip.com",
        "https://api.ipify.org?format=json"
    ]
    
    print("=== Testing Direct Connection to Validation URLs ===")
    for url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10, verify=False)
            response_time = time.time() - start_time
            print(f"✅ {url}: Status {response.status_code}, Time: {response_time:.2f}s")
            if response.status_code == 200:
                print(f"   Response: {response.text[:100]}")
        except Exception as e:
            print(f"❌ {url}: Error - {e}")
    print()

def get_sample_proxies():
    """Get sample proxies from GeoNode API."""
    print("=== Fetching Sample Proxies from GeoNode API ===")
    try:
        url = "https://proxylist.geonode.com/api/proxy-list?limit=5&page=1&sort_by=lastChecked&sort_type=desc&country=CN"
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            data = response.json()
            proxies = []
            for proxy_data in data.get('data', []):
                proxy = {
                    'ip': proxy_data.get('ip'),
                    'port': proxy_data.get('port'),
                    'protocols': proxy_data.get('protocols', []),
                    'anonymityLevel': proxy_data.get('anonymityLevel'),
                    'upTime': proxy_data.get('upTime'),
                    'responseTime': proxy_data.get('responseTime')
                }
                proxies.append(proxy)
            print(f"✅ Retrieved {len(proxies)} sample proxies")
            for i, proxy in enumerate(proxies):
                print(f"   Proxy {i+1}: {proxy['ip']}:{proxy['port']} ({proxy['protocols']}) - {proxy['anonymityLevel']}")
            return proxies
        else:
            print(f"❌ Failed to fetch proxies: Status {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error fetching proxies: {e}")
        return []

def test_proxy_validation(proxy: Dict[str, Any], test_url: str = "http://httpbin.org/ip") -> Dict[str, Any]:
    """Test validation of a single proxy."""
    # Construct proxy URL based on proxy type
    protocols = proxy.get('protocols', ['http'])
    if 'socks4' in protocols:
        proxy_url = f"socks4://{proxy['ip']}:{proxy['port']}"
    elif 'socks5' in protocols:
        proxy_url = f"socks5://{proxy['ip']}:{proxy['port']}"
    elif 'https' in protocols:
        proxy_url = f"https://{proxy['ip']}:{proxy['port']}"
    else:  # Default to HTTP
        proxy_url = f"http://{proxy['ip']}:{proxy['port']}"

    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    print(f"Testing proxy {proxy['ip']}:{proxy['port']} with {test_url}")
    
    start_time = time.time()
    try:
        response = requests.get(
            test_url,
            proxies=proxies,
            timeout=10,
            verify=False,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        response_time = time.time() - start_time
        
        result = {
            'success': response.status_code == 200,
            'status_code': response.status_code,
            'response_time': response_time,
            'response_text': response.text[:200] if response.status_code == 200 else None,
            'error': None
        }
        
        if response.status_code == 200:
            print(f"   ✅ Success: Status {response.status_code}, Time: {response_time:.2f}s")
            print(f"   Response: {response.text[:100]}")
        else:
            print(f"   ⚠ HTTP Error: Status {response.status_code}, Time: {response_time:.2f}s")
            
        return result
        
    except requests.exceptions.Timeout:
        response_time = time.time() - start_time
        print(f"   ❌ Timeout after {response_time:.2f}s")
        return {
            'success': False,
            'response_time': response_time,
            'error': 'timeout'
        }
    except requests.exceptions.ConnectionError as e:
        response_time = time.time() - start_time
        print(f"   ❌ Connection Error: {e}")
        return {
            'success': False,
            'response_time': response_time,
            'error': f'connection_error: {str(e)}'
        }
    except Exception as e:
        response_time = time.time() - start_time
        print(f"   ❌ Other Error: {e}")
        return {
            'success': False,
            'response_time': response_time,
            'error': f'other: {str(e)}'
        }

def main():
    """Main debug function."""
    print("🔍 PROXY VALIDATION DEBUG SCRIPT")
    print("=" * 50)
    
    # Test direct connection first
    test_direct_connection()
    
    # Get sample proxies
    proxies = get_sample_proxies()
    if not proxies:
        print("❌ No proxies to test. Exiting.")
        return
    
    print("\n=== Testing Individual Proxy Validation ===")
    
    test_urls = [
        "http://httpbin.org/ip",
        "http://icanhazip.com"
    ]
    
    for i, proxy in enumerate(proxies[:3]):  # Test first 3 proxies
        print(f"\n--- Testing Proxy {i+1}: {proxy['ip']}:{proxy['port']} ---")
        print(f"Protocols: {proxy['protocols']}")
        print(f"Anonymity: {proxy['anonymityLevel']}")
        print(f"Uptime: {proxy['upTime']}%")
        
        for test_url in test_urls:
            result = test_proxy_validation(proxy, test_url)
            time.sleep(1)  # Brief delay between tests
    
    print("\n=== Debug Complete ===")

if __name__ == "__main__":
    main()
