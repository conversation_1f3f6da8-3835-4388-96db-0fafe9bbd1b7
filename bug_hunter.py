#!/usr/bin/env python3
"""
Bug hunter script to identify the exact issue preventing proxy validation.
Tests proxies with minimal code to isolate the problem.
"""

import requests
import time
import urllib3
from typing import Dict, Any

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_known_working_proxy():
    """Test with a known working public proxy to verify our testing logic."""
    print("🔍 Testing with known working public proxies...")
    
    # These are commonly available public HTTP proxies (may or may not work)
    known_proxies = [
        {"ip": "*******", "port": "3128", "type": "http"},  # This won't work, just for testing
        {"ip": "proxy.example.com", "port": "8080", "type": "http"},  # This won't work either
    ]
    
    # Let's test without any proxy first to make sure our test URLs work
    print("\n--- Testing direct connection (no proxy) ---")
    test_urls = ["http://httpbin.org/ip", "http://icanhazip.com"]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10, verify=False)
            if response.status_code == 200:
                print(f"✅ Direct connection to {url}: {response.text[:50]}")
            else:
                print(f"❌ Direct connection failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Direct connection error: {e}")

def test_geonode_proxy_directly():
    """Get a proxy from GeoNode and test it with minimal code."""
    print("\n🔍 Testing GeoNode proxy with minimal code...")
    
    # Get one proxy from GeoNode
    try:
        api_url = "https://proxylist.geonode.com/api/proxy-list?limit=1&page=1&sort_by=lastChecked&sort_type=desc&country=US"
        response = requests.get(api_url, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ GeoNode API failed: {response.status_code}")
            return
            
        data = response.json()
        proxies = data.get('data', [])
        
        if not proxies:
            print("❌ No proxies returned from GeoNode")
            return
            
        proxy_data = proxies[0]
        ip = proxy_data.get('ip')
        port = proxy_data.get('port')
        protocols = proxy_data.get('protocols', [])
        uptime = proxy_data.get('upTime', 0)
        
        print(f"📊 Retrieved proxy: {ip}:{port}")
        print(f"   Protocols: {protocols}")
        print(f"   Uptime: {uptime}%")
        
        # Test with each protocol
        for protocol in protocols:
            print(f"\n--- Testing {protocol.upper()} protocol ---")
            
            # Construct proxy URL
            if protocol.lower() == 'socks4':
                proxy_url = f"socks4://{ip}:{port}"
            elif protocol.lower() == 'socks5':
                proxy_url = f"socks5://{ip}:{port}"
            elif protocol.lower() == 'https':
                proxy_url = f"https://{ip}:{port}"
            else:
                proxy_url = f"http://{ip}:{port}"
            
            print(f"Using proxy URL: {proxy_url}")
            
            proxies_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # Test with simple URL
            test_url = "http://httpbin.org/ip"
            print(f"Testing {test_url}...")
            
            try:
                start_time = time.time()
                response = requests.get(
                    test_url,
                    proxies=proxies_dict,
                    timeout=15,  # Longer timeout
                    verify=False,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                response_time = time.time() - start_time
                
                print(f"Response status: {response.status_code}")
                print(f"Response time: {response_time:.2f}s")
                
                if response.status_code == 200:
                    print(f"✅ SUCCESS! Response: {response.text[:100]}")
                    return True
                else:
                    print(f"❌ HTTP Error: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"❌ Timeout after 15 seconds")
            except requests.exceptions.ConnectionError as e:
                print(f"❌ Connection Error: {str(e)[:200]}")
            except Exception as e:
                print(f"❌ Other Error: {str(e)[:200]}")
                
    except Exception as e:
        print(f"❌ Error getting proxy from GeoNode: {e}")
        
    return False

def test_multiple_geonode_proxies():
    """Test multiple proxies from GeoNode to find at least one working."""
    print("\n🔍 Testing multiple GeoNode proxies...")
    
    try:
        # Get 10 proxies from different countries
        countries = ['US', 'GB', 'DE', 'FR', 'CA']
        working_proxies = 0
        
        for country in countries:
            print(f"\n--- Testing {country} proxies ---")
            api_url = f"https://proxylist.geonode.com/api/proxy-list?limit=2&page=1&sort_by=lastChecked&sort_type=desc&country={country}"
            
            try:
                response = requests.get(api_url, timeout=30)
                if response.status_code != 200:
                    continue
                    
                data = response.json()
                proxies = data.get('data', [])
                
                for proxy_data in proxies:
                    ip = proxy_data.get('ip')
                    port = proxy_data.get('port')
                    protocols = proxy_data.get('protocols', [])
                    uptime = proxy_data.get('upTime', 0)
                    
                    print(f"Testing {ip}:{port} (uptime: {uptime}%)")
                    
                    # Try HTTP first (most common)
                    if 'http' in protocols:
                        proxy_url = f"http://{ip}:{port}"
                        proxies_dict = {'http': proxy_url, 'https': proxy_url}
                        
                        try:
                            response = requests.get(
                                "http://httpbin.org/ip",
                                proxies=proxies_dict,
                                timeout=10,
                                verify=False
                            )
                            
                            if response.status_code == 200:
                                print(f"✅ WORKING PROXY FOUND: {ip}:{port}")
                                print(f"   Response: {response.text[:100]}")
                                working_proxies += 1
                                
                        except:
                            pass  # Continue to next proxy
                            
            except:
                continue  # Continue to next country
                
        print(f"\n📊 Found {working_proxies} working proxies")
        return working_proxies > 0
        
    except Exception as e:
        print(f"❌ Error in multiple proxy test: {e}")
        return False

def main():
    """Run bug hunting tests."""
    print("🐛 BUG HUNTER - Finding the validation issue")
    print("=" * 50)
    
    # Test 1: Verify our test setup works
    test_known_working_proxy()
    
    # Test 2: Test one GeoNode proxy thoroughly
    if test_geonode_proxy_directly():
        print("\n🎉 Found working proxy! The issue might be in the unified system logic.")
        return
    
    # Test 3: Test multiple proxies to find at least one working
    if test_multiple_geonode_proxies():
        print("\n🎉 Found working proxies! The issue is definitely in the unified system.")
    else:
        print("\n🤔 No working proxies found. This suggests either:")
        print("   1. Network/firewall issues preventing proxy connections")
        print("   2. All tested proxies are genuinely non-functional")
        print("   3. There's still a bug in our testing logic")

if __name__ == "__main__":
    main()
