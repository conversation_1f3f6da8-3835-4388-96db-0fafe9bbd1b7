"""
SOCKS5 Proxy Handler for Russian Proxy Server
Handles SOCKS5 protocol with authentication and upstream proxy forwarding.
"""

import socket
import struct
import threading
import time
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from proxy_server_base import Base<PERSON>ro<PERSON><PERSON><PERSON><PERSON>, ProxyServer<PERSON>onfig, Authentication<PERSON>anager, UpstreamProxy
from proxy_pool_manager import ProxyPoolManager


class SOCKS5ProxyHandler(BaseProxyHandler):
    """Handles SOCKS5 proxy connections."""
    
    # SOCKS5 constants
    SOCKS_VERSION = 0x05
    
    # Authentication methods
    AUTH_NO_AUTH = 0x00
    AUTH_USERNAME_PASSWORD = 0x02
    AUTH_NO_ACCEPTABLE = 0xFF
    
    # Commands
    CMD_CONNECT = 0x01
    CMD_BIND = 0x02
    CMD_UDP_ASSOCIATE = 0x03
    
    # Address types
    ADDR_IPV4 = 0x01
    ADDR_DOMAIN = 0x03
    ADDR_IPV6 = 0x04
    
    # Reply codes
    REPLY_SUCCESS = 0x00
    REPLY_GENERAL_FAILURE = 0x01
    REPLY_CONNECTION_NOT_ALLOWED = 0x02
    REPLY_NETWORK_UNREACHABLE = 0x03
    REPLY_HOST_UNREACHABLE = 0x04
    REPLY_CONNECTION_REFUSED = 0x05
    REPLY_TTL_EXPIRED = 0x06
    REPLY_COMMAND_NOT_SUPPORTED = 0x07
    REPLY_ADDRESS_TYPE_NOT_SUPPORTED = 0x08
    
    def __init__(self, config: ProxyServerConfig, auth_manager: AuthenticationManager, 
                 pool_manager: ProxyPoolManager):
        """Initialize SOCKS5 proxy handler."""
        super().__init__(config, auth_manager)
        self.pool_manager = pool_manager
        self.socks_config = config.get_protocol_config('socks5')
        self.buffer_size = config.get('server', 'buffer_size', 8192)
        self.connect_timeout = self.socks_config.get('connect_timeout', 15)
        self.resolve_dns = self.socks_config.get('resolve_dns', True)
    
    def handle_connection(self, client_socket: socket.socket, client_address: Tuple[str, int]) -> None:
        """Handle incoming SOCKS5 connection."""
        conn_id = self.start_connection_stats(client_address, 'SOCKS5')
        
        try:
            # Set socket timeout
            client_socket.settimeout(self.connect_timeout)
            
            # SOCKS5 handshake
            if not self._handle_handshake(client_socket):
                self.end_connection_stats(conn_id, "Handshake failed")
                return
            
            # Authentication
            if not self._handle_authentication(client_socket):
                self.end_connection_stats(conn_id, "Authentication failed")
                return
            
            # Handle request
            self._handle_request(client_socket, conn_id)
            
        except Exception as e:
            self.logger.error(f"Error handling SOCKS5 connection from {client_address[0]}: {e}")
            self.end_connection_stats(conn_id, str(e))
        finally:
            self.close_socket_safely(client_socket)
    
    def _handle_handshake(self, client_socket: socket.socket) -> bool:
        """Handle SOCKS5 initial handshake."""
        try:
            # Read greeting
            data = client_socket.recv(2)
            if len(data) != 2:
                return False
            
            version, nmethods = struct.unpack('!BB', data)
            if version != self.SOCKS_VERSION:
                self.logger.debug(f"Unsupported SOCKS version: {version}")
                return False
            
            # Read authentication methods
            methods_data = client_socket.recv(nmethods)
            if len(methods_data) != nmethods:
                return False
            
            methods = list(methods_data)
            
            # Choose authentication method
            if self.auth_manager.enabled:
                if self.AUTH_USERNAME_PASSWORD in methods:
                    chosen_method = self.AUTH_USERNAME_PASSWORD
                else:
                    chosen_method = self.AUTH_NO_ACCEPTABLE
            else:
                if self.AUTH_NO_AUTH in methods:
                    chosen_method = self.AUTH_NO_AUTH
                else:
                    chosen_method = self.AUTH_NO_ACCEPTABLE
            
            # Send method selection response
            response = struct.pack('!BB', self.SOCKS_VERSION, chosen_method)
            client_socket.send(response)
            
            return chosen_method != self.AUTH_NO_ACCEPTABLE
            
        except Exception as e:
            self.logger.debug(f"SOCKS5 handshake error: {e}")
            return False
    
    def _handle_authentication(self, client_socket: socket.socket) -> bool:
        """Handle SOCKS5 authentication."""
        if not self.auth_manager.enabled:
            return True
        
        try:
            # Read authentication request
            data = client_socket.recv(2)
            if len(data) != 2:
                return False
            
            version, username_len = struct.unpack('!BB', data)
            if version != 0x01:  # Username/password auth version
                return False
            
            # Read username
            username_data = client_socket.recv(username_len)
            if len(username_data) != username_len:
                return False
            
            username = username_data.decode('utf-8')
            
            # Read password length
            password_len_data = client_socket.recv(1)
            if len(password_len_data) != 1:
                return False
            
            password_len = struct.unpack('!B', password_len_data)[0]
            
            # Read password
            password_data = client_socket.recv(password_len)
            if len(password_data) != password_len:
                return False
            
            password = password_data.decode('utf-8')
            
            # Validate credentials
            auth_success = self.auth_manager.validate_socks5_auth(username, password)
            
            # Send authentication response
            status = 0x00 if auth_success else 0x01
            response = struct.pack('!BB', 0x01, status)
            client_socket.send(response)
            
            return auth_success
            
        except Exception as e:
            self.logger.debug(f"SOCKS5 authentication error: {e}")
            return False
    
    def _handle_request(self, client_socket: socket.socket, conn_id: str) -> None:
        """Handle SOCKS5 request."""
        try:
            # Read request header
            data = client_socket.recv(4)
            if len(data) != 4:
                self._send_reply(client_socket, self.REPLY_GENERAL_FAILURE)
                return
            
            version, cmd, reserved, addr_type = struct.unpack('!BBBB', data)
            
            if version != self.SOCKS_VERSION:
                self._send_reply(client_socket, self.REPLY_GENERAL_FAILURE)
                return
            
            # Only support CONNECT command
            if cmd != self.CMD_CONNECT:
                self._send_reply(client_socket, self.REPLY_COMMAND_NOT_SUPPORTED)
                return
            
            # Parse destination address
            target_host, target_port = self._parse_address(client_socket, addr_type)
            if not target_host or not target_port:
                self._send_reply(client_socket, self.REPLY_ADDRESS_TYPE_NOT_SUPPORTED)
                return
            
            self.logger.debug(f"SOCKS5 CONNECT to {target_host}:{target_port}")
            
            # Get upstream proxy
            upstream_proxy = self.pool_manager.get_proxy()
            if not upstream_proxy:
                self._send_reply(client_socket, self.REPLY_GENERAL_FAILURE)
                return
            
            # Connect through upstream proxy
            upstream_socket = self._connect_through_upstream(upstream_proxy, target_host, target_port)
            if not upstream_socket:
                self.pool_manager.record_proxy_failure(upstream_proxy)
                self._send_reply(client_socket, self.REPLY_HOST_UNREACHABLE)
                return
            
            # Send success reply
            self._send_reply(client_socket, self.REPLY_SUCCESS, target_host, target_port)
            
            # Update statistics
            self.update_connection_stats(conn_id, upstream_proxy=str(upstream_proxy))
            self.pool_manager.record_proxy_success(upstream_proxy)
            
            # Start tunneling
            self._tunnel_data(client_socket, upstream_socket, conn_id)
            
        except Exception as e:
            self.logger.error(f"Error handling SOCKS5 request: {e}")
            self._send_reply(client_socket, self.REPLY_GENERAL_FAILURE)
    
    def _parse_address(self, client_socket: socket.socket, addr_type: int) -> Tuple[Optional[str], Optional[int]]:
        """Parse destination address from SOCKS5 request."""
        try:
            if addr_type == self.ADDR_IPV4:
                # IPv4 address (4 bytes)
                addr_data = client_socket.recv(4)
                if len(addr_data) != 4:
                    return None, None
                host = socket.inet_ntoa(addr_data)
                
            elif addr_type == self.ADDR_DOMAIN:
                # Domain name
                domain_len_data = client_socket.recv(1)
                if len(domain_len_data) != 1:
                    return None, None
                
                domain_len = struct.unpack('!B', domain_len_data)[0]
                domain_data = client_socket.recv(domain_len)
                if len(domain_data) != domain_len:
                    return None, None
                
                host = domain_data.decode('utf-8')
                
            elif addr_type == self.ADDR_IPV6:
                # IPv6 address (16 bytes)
                addr_data = client_socket.recv(16)
                if len(addr_data) != 16:
                    return None, None
                host = socket.inet_ntop(socket.AF_INET6, addr_data)
                
            else:
                return None, None
            
            # Read port (2 bytes)
            port_data = client_socket.recv(2)
            if len(port_data) != 2:
                return None, None
            
            port = struct.unpack('!H', port_data)[0]
            
            return host, port
            
        except Exception as e:
            self.logger.debug(f"Error parsing SOCKS5 address: {e}")
            return None, None
    
    def _connect_through_upstream(self, upstream_proxy: UpstreamProxy, target_host: str, 
                                target_port: int) -> Optional[socket.socket]:
        """Connect to target through upstream proxy."""
        try:
            # Create connection to upstream proxy
            upstream_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            upstream_socket.settimeout(self.connect_timeout)
            upstream_socket.connect((upstream_proxy.ip, upstream_proxy.port))
            
            if upstream_proxy.protocol in ['HTTP', 'HTTPS']:
                # Use HTTP CONNECT method
                connect_request = (
                    f"CONNECT {target_host}:{target_port} HTTP/1.1\r\n"
                    f"Host: {target_host}:{target_port}\r\n"
                    "\r\n"
                )
                
                upstream_socket.send(connect_request.encode())
                
                # Read response
                response = upstream_socket.recv(self.buffer_size).decode()
                if "200" not in response.split('\r\n')[0]:
                    upstream_socket.close()
                    return None
                
                return upstream_socket
                
            elif upstream_proxy.protocol == 'SOCKS5':
                # Chain SOCKS5 proxies
                return self._connect_socks5_upstream(upstream_socket, target_host, target_port)
            
            elif upstream_proxy.protocol == 'SOCKS4':
                # Connect through SOCKS4 proxy
                return self._connect_socks4_upstream(upstream_socket, target_host, target_port)
            
            else:
                upstream_socket.close()
                return None
                
        except Exception as e:
            self.logger.debug(f"Failed to connect through upstream {upstream_proxy}: {e}")
            return None
    
    def _connect_socks5_upstream(self, sock: socket.socket, host: str, port: int) -> Optional[socket.socket]:
        """Connect through upstream SOCKS5 proxy."""
        try:
            # SOCKS5 greeting
            sock.send(b'\x05\x01\x00')  # Version 5, 1 method, no auth
            response = sock.recv(2)
            
            if response != b'\x05\x00':
                return None
            
            # SOCKS5 connect request
            request = b'\x05\x01\x00'  # Version 5, connect, reserved
            
            # Add destination address
            try:
                # Try to parse as IP address
                import ipaddress
                ip = ipaddress.ip_address(host)
                if ip.version == 4:
                    request += b'\x01' + socket.inet_aton(host)
                else:
                    request += b'\x04' + socket.inet_pton(socket.AF_INET6, host)
            except:
                # Use domain name
                host_bytes = host.encode()
                request += b'\x03' + bytes([len(host_bytes)]) + host_bytes
            
            # Add port
            request += port.to_bytes(2, 'big')
            
            sock.send(request)
            response = sock.recv(10)
            
            if len(response) < 2 or response[1] != 0:
                return None
            
            return sock
            
        except Exception as e:
            self.logger.debug(f"SOCKS5 upstream connection failed: {e}")
            return None
    
    def _connect_socks4_upstream(self, sock: socket.socket, host: str, port: int) -> Optional[socket.socket]:
        """Connect through upstream SOCKS4 proxy."""
        try:
            # Resolve hostname to IP if needed
            try:
                import ipaddress
                ip = ipaddress.ip_address(host)
                ip_bytes = socket.inet_aton(str(ip))
            except:
                # Resolve hostname
                ip_bytes = socket.inet_aton(socket.gethostbyname(host))
            
            # SOCKS4 connect request
            request = struct.pack('!BBH', 0x04, 0x01, port) + ip_bytes + b'\x00'
            sock.send(request)
            
            # Read response
            response = sock.recv(8)
            if len(response) != 8 or response[1] != 0x5A:
                return None
            
            return sock
            
        except Exception as e:
            self.logger.debug(f"SOCKS4 upstream connection failed: {e}")
            return None
    
    def _send_reply(self, client_socket: socket.socket, reply_code: int, 
                   bind_host: str = '0.0.0.0', bind_port: int = 0) -> None:
        """Send SOCKS5 reply."""
        try:
            # Build reply
            reply = struct.pack('!BBBB', self.SOCKS_VERSION, reply_code, 0x00, self.ADDR_IPV4)
            reply += socket.inet_aton(bind_host)
            reply += struct.pack('!H', bind_port)
            
            client_socket.send(reply)
            
        except Exception as e:
            self.logger.debug(f"Error sending SOCKS5 reply: {e}")
    
    def _tunnel_data(self, client_socket: socket.socket, upstream_socket: socket.socket, 
                    conn_id: str) -> None:
        """Tunnel data between client and upstream sockets."""
        def forward_data(source: socket.socket, destination: socket.socket, direction: str):
            try:
                while True:
                    data = source.recv(self.buffer_size)
                    if not data:
                        break
                    
                    destination.send(data)
                    
                    # Update statistics
                    if direction == 'client_to_upstream':
                        self.update_connection_stats(conn_id, bytes_sent=len(data))
                    else:
                        self.update_connection_stats(conn_id, bytes_received=len(data))
                        
            except Exception as e:
                self.logger.debug(f"SOCKS5 tunneling error ({direction}): {e}")
            finally:
                self.close_socket_safely(source)
                self.close_socket_safely(destination)
        
        # Start forwarding threads
        client_to_upstream = threading.Thread(
            target=forward_data,
            args=(client_socket, upstream_socket, 'client_to_upstream'),
            daemon=True
        )
        
        upstream_to_client = threading.Thread(
            target=forward_data,
            args=(upstream_socket, client_socket, 'upstream_to_client'),
            daemon=True
        )
        
        client_to_upstream.start()
        upstream_to_client.start()
        
        # Wait for both threads to complete
        client_to_upstream.join()
        upstream_to_client.join()
