#!/usr/bin/env python3
"""
Unified Proxy System
A self-contained Python script that combines proxy collection, validation, and server functionality.
Supports both Russian (RU) and Chinese (CN) proxy collection with integrated proxy server.

Usage:
    python unified_proxy_system.py --country CN --collect --serve --test
    python unified_proxy_system.py --country RU --collect-only --output proxies.csv
    python unified_proxy_system.py --serve --proxy-file existing_proxies.csv
"""

import argparse
import base64
import csv
import logging
import re
import signal
import socket
import sys
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import concurrent.futures

# Import required libraries with fallback error handling
try:
    import requests
    from bs4 import BeautifulSoup
    import urllib3
    # Suppress SSL certificate warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
except ImportError as e:
    print(f"Error: Required libraries not installed. Please run: pip install requests beautifulsoup4")
    sys.exit(1)


# ============================================================================
# EMBEDDED CONFIGURATION
# ============================================================================

EMBEDDED_CONFIG = {
    "proxy_sources": {
        "RU": {
            "proxynova": {
                "name": "ProxyNova",
                "url": "https://www.proxynova.com/proxy-server-list/country-ru",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30
            },
            "spys_one": {
                "name": "Spys.One",
                "url": "https://spys.one/free-proxy-list/RU/",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30
            },
            "free_proxy_list": {
                "name": "Free Proxy List",
                "url": "https://free-proxy-list.net/",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30,
                "country_filter": "RU"
            },
            "geonode": {
                "name": "GeoNode",
                "url": "https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc&country=RU",
                "method": "api",
                "enabled": True,
                "rate_limit": 1.0,
                "timeout": 30
            }
        },
        "CN": {
            "proxynova": {
                "name": "ProxyNova",
                "url": "https://www.proxynova.com/proxy-server-list/country-cn",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30
            },
            "spys_one": {
                "name": "Spys.One",
                "url": "https://spys.one/free-proxy-list/CN/",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30
            },
            "free_proxy_list": {
                "name": "Free Proxy List",
                "url": "https://free-proxy-list.net/",
                "method": "html_scraping",
                "enabled": True,
                "rate_limit": 2.0,
                "timeout": 30,
                "country_filter": "CN"
            },
            "geonode": {
                "name": "GeoNode",
                "url": "https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc&country=CN",
                "method": "api",
                "enabled": True,
                "rate_limit": 1.0,
                "timeout": 30
            }
        }
    },
    "validation": {
        "test_urls": [
            "http://httpbin.org/ip",
            "https://httpbin.org/ip",
            "http://icanhazip.com"
        ],
        "timeout": 10,
        "max_retries": 2,
        "concurrent_tests": 30,
        "success_threshold": 0.5
    },
    "server": {
        "http_port": 8080,
        "socks_port": 1080,
        "bind_address": "127.0.0.1",
        "max_connections": 100,
        "connection_timeout": 30,
        "buffer_size": 8192
    },
    "authentication": {
        "enabled": True,
        "username": "proxyuser",
        "password": "proxypass",
        "realm": "Unified Proxy Server"
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    }
}


# ============================================================================
# DATA CLASSES
# ============================================================================

@dataclass
class ProxyInfo:
    """Data class representing a proxy server."""
    ip: str
    port: int
    type: str = "HTTP"
    country: str = "Unknown"
    anonymity: str = "Unknown"
    response_time: Optional[float] = None
    last_tested: Optional[str] = None
    status: str = "Unknown"
    source: str = ""
    uptime: Optional[float] = None
    speed: Optional[int] = None
    failures: int = 0
    success_count: int = 0
    total_requests: int = 0
    
    def __post_init__(self):
        """Validate and normalize proxy data after initialization."""
        if not self._is_valid_ip(self.ip):
            raise ValueError(f"Invalid IP address: {self.ip}")
        
        if not (1 <= self.port <= 65535):
            raise ValueError(f"Invalid port number: {self.port}")
        
        self.type = self.type.upper()
        if self.type not in ["HTTP", "HTTPS", "SOCKS4", "SOCKS5"]:
            self.type = "HTTP"
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format."""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    @property
    def is_healthy(self) -> bool:
        """Check if proxy is considered healthy."""
        return self.status == "Valid" and self.failures < 3
    
    def record_success(self):
        """Record a successful request."""
        self.success_count += 1
        self.total_requests += 1
        self.failures = 0
    
    def record_failure(self):
        """Record a failed request."""
        self.failures += 1
        self.total_requests += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert proxy info to dictionary."""
        return {
            'ip': self.ip,
            'port': self.port,
            'type': self.type,
            'country': self.country,
            'anonymity': self.anonymity,
            'response_time': self.response_time,
            'last_tested': self.last_tested,
            'status': self.status,
            'source': self.source,
            'uptime': self.uptime,
            'speed': self.speed
        }
    
    def __str__(self) -> str:
        """String representation of proxy."""
        return f"{self.ip}:{self.port} ({self.type}, {self.anonymity})"


# ============================================================================
# BASE SCRAPER CLASS
# ============================================================================

class BaseScraper(ABC):
    """Abstract base class for proxy scrapers."""
    
    def __init__(self, name: str, config: Dict[str, Any], country: str):
        """Initialize the base scraper."""
        self.name = name
        self.config = config
        self.country = country
        self.logger = logging.getLogger(f"scraper.{name}")
        self.session = requests.Session()
        self._setup_session()
        
        # Rate limiting
        self.last_request_time = 0
        self.rate_limit = config.get('rate_limit', 1.0)
        
        # Statistics
        self.stats = {
            'requests_made': 0,
            'proxies_found': 0,
            'errors': 0,
            'last_run': None
        }
    
    def _setup_session(self) -> None:
        """Setup the requests session with headers and timeouts."""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session.headers.update(headers)
        self.session.timeout = self.config.get('timeout', 30)
        # Disable SSL verification to avoid certificate warnings
        self.session.verify = False
    
    def _rate_limit_wait(self) -> None:
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit:
            sleep_time = self.rate_limit - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, url: str, **kwargs) -> requests.Response:
        """Make a rate-limited HTTP request."""
        self._rate_limit_wait()
        
        try:
            self.logger.debug(f"Making request to: {url}")
            response = self.session.get(url, verify=False, **kwargs)
            response.raise_for_status()
            self.stats['requests_made'] += 1
            return response
            
        except requests.RequestException as e:
            self.stats['errors'] += 1
            self.logger.error(f"Request failed for {url}: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        text = text.replace('&nbsp;', ' ')
        return text
    
    @abstractmethod
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from the source."""
        pass


# ============================================================================
# SPECIFIC SCRAPERS
# ============================================================================

class GeoNodeScraper(BaseScraper):
    """Scraper for GeoNode API."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from GeoNode API."""
        self.logger.info(f"Scraping {self.country} proxies from GeoNode API")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            data = response.json()
            
            if 'data' not in data:
                self.logger.warning("No data field in GeoNode API response")
                return proxies
            
            for proxy_data in data['data']:
                try:
                    ip = proxy_data.get('ip', '').strip()
                    port = proxy_data.get('port')
                    
                    if not ip or not port:
                        continue
                    
                    # Extract proxy type from protocols
                    protocols = proxy_data.get('protocols', [])
                    proxy_type = "HTTP"
                    if protocols:
                        if 'socks5' in protocols:
                            proxy_type = "SOCKS5"
                        elif 'socks4' in protocols:
                            proxy_type = "SOCKS4"
                        elif 'https' in protocols:
                            proxy_type = "HTTPS"
                    
                    # Extract anonymity level
                    anonymity_level = proxy_data.get('anonymityLevel', 'unknown').lower()
                    if anonymity_level == 'elite':
                        anonymity = "Elite"
                    elif anonymity_level == 'anonymous':
                        anonymity = "Anonymous"
                    elif anonymity_level == 'transparent':
                        anonymity = "Transparent"
                    else:
                        anonymity = "Unknown"
                    
                    # Extract additional information
                    uptime = proxy_data.get('upTime')
                    speed = proxy_data.get('speed')
                    response_time = proxy_data.get('responseTime')
                    
                    # Convert response time from milliseconds to seconds
                    if response_time:
                        response_time = response_time / 1000.0
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=int(port),
                        type=proxy_type,
                        country=self.country,
                        anonymity=anonymity,
                        response_time=response_time,
                        source=self.name,
                        uptime=uptime,
                        speed=speed,
                        status="Valid"  # GeoNode provides working proxies
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing GeoNode proxy data: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} {self.country} proxies from GeoNode API")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape GeoNode API: {e}")
            raise
        
        return proxies


class ProxyNovaScraper(BaseScraper):
    """Scraper for ProxyNova.com proxy list."""
    
    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from ProxyNova."""
        self.logger.info(f"Scraping {self.country} proxies from ProxyNova")
        proxies = []
        
        try:
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the proxy table
            proxy_table = soup.find('table')
            if not proxy_table:
                self.logger.warning("Could not find proxy table on ProxyNova")
                return proxies
            
            rows = proxy_table.find_all('tr')[1:]  # Skip header row
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 7:
                        continue
                    
                    # Extract proxy information
                    ip_cell = cells[0]
                    port_cell = cells[1]
                    
                    # Extract IP (handle potential obfuscation)
                    ip = self._extract_ip_from_cell(ip_cell)
                    port_text = self._clean_text(port_cell.get_text())
                    
                    if not ip or not port_text.isdigit():
                        continue
                    
                    port = int(port_text)
                    
                    # Extract anonymity
                    anonymity = "Unknown"
                    if len(cells) > 6:
                        anonymity_text = self._clean_text(cells[6].get_text()).lower()
                        if 'elite' in anonymity_text or 'high' in anonymity_text:
                            anonymity = "Elite"
                        elif 'anonymous' in anonymity_text:
                            anonymity = "Anonymous"
                        elif 'transparent' in anonymity_text:
                            anonymity = "Transparent"
                    
                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type="HTTP",
                        country=self.country,
                        anonymity=anonymity,
                        source=self.name,
                        status="Unknown"
                    )
                    
                    proxies.append(proxy)
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing ProxyNova row: {e}")
                    continue
            
            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} {self.country} proxies from ProxyNova")
            
        except Exception as e:
            self.logger.error(f"Failed to scrape ProxyNova: {e}")
            raise
        
        return proxies
    
    def _extract_ip_from_cell(self, cell) -> Optional[str]:
        """Extract IP address from ProxyNova cell."""
        text = self._clean_text(cell.get_text())
        ip_pattern = r'\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b'
        match = re.search(ip_pattern, text)
        return match.group(1) if match else None


class FreeProxyListScraper(BaseScraper):
    """Scraper for Free-Proxy-List.net."""

    def scrape_proxies(self) -> List[ProxyInfo]:
        """Scrape proxies from Free-Proxy-List.net."""
        self.logger.info(f"Scraping {self.country} proxies from Free-Proxy-List.net")
        proxies = []

        try:
            response = self._make_request(self.config['url'])
            soup = BeautifulSoup(response.text, 'html.parser')

            # Find the proxy table
            proxy_table = soup.find('table', {'id': 'proxylisttable'})
            if not proxy_table:
                proxy_table = soup.find('table')

            if not proxy_table:
                self.logger.warning("Could not find proxy table on Free-Proxy-List.net")
                return proxies

            tbody = proxy_table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
            else:
                rows = proxy_table.find_all('tr')[1:]  # Skip header

            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 7:
                        continue

                    ip = self._clean_text(cells[0].get_text())
                    port_text = self._clean_text(cells[1].get_text())
                    country_code = self._clean_text(cells[2].get_text())
                    anonymity = self._clean_text(cells[4].get_text())
                    https = self._clean_text(cells[6].get_text())

                    # Filter for specific country
                    country_filter = self.config.get('country_filter')
                    if country_filter and country_code != country_filter:
                        continue

                    if not port_text.isdigit():
                        continue

                    port = int(port_text)

                    # Determine proxy type
                    proxy_type = "HTTPS" if https.lower() == "yes" else "HTTP"

                    # Map anonymity
                    if anonymity.lower() == "elite proxy":
                        anonymity = "Elite"
                    elif anonymity.lower() == "anonymous":
                        anonymity = "Anonymous"
                    else:
                        anonymity = "Transparent"

                    proxy = ProxyInfo(
                        ip=ip,
                        port=port,
                        type=proxy_type,
                        country=self.country,
                        anonymity=anonymity,
                        source=self.name,
                        status="Unknown"
                    )

                    proxies.append(proxy)

                except Exception as e:
                    self.logger.debug(f"Error parsing Free-Proxy-List row: {e}")
                    continue

            self.stats['proxies_found'] = len(proxies)
            self.stats['last_run'] = datetime.now().isoformat()
            self.logger.info(f"Found {len(proxies)} {self.country} proxies from Free-Proxy-List.net")

        except Exception as e:
            self.logger.error(f"Failed to scrape Free-Proxy-List.net: {e}")
            raise

        return proxies


# ============================================================================
# PROXY VALIDATOR
# ============================================================================

class ProxyValidator:
    """Validates proxy servers by testing connectivity."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the proxy validator."""
        self.config = config.get('validation', {})
        self.logger = logging.getLogger('validator')

        self.test_urls = self.config.get('test_urls', [])
        self.timeout = self.config.get('timeout', 10)
        self.max_retries = self.config.get('max_retries', 2)
        self.concurrent_tests = self.config.get('concurrent_tests', 30)
        self.success_threshold = self.config.get('success_threshold', 0.5)

        self.stats = {
            'total_tested': 0,
            'valid_proxies': 0,
            'invalid_proxies': 0
        }

    def validate_proxy(self, proxy: ProxyInfo) -> ProxyInfo:
        """Validate a single proxy."""
        start_time = time.time()
        successful_tests = 0
        total_tests = 0
        response_times = []

        # Test proxy with multiple URLs
        for test_url in self.test_urls:
            for attempt in range(self.max_retries + 1):
                try:
                    result = self._test_proxy_url(proxy, test_url)
                    if result['success']:
                        successful_tests += 1
                        response_times.append(result['response_time'])
                    total_tests += 1
                    break  # Success, no need to retry

                except Exception:
                    if attempt == self.max_retries:
                        total_tests += 1
                    else:
                        time.sleep(0.5)  # Brief delay before retry

        # Calculate results
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        avg_response_time = sum(response_times) / len(response_times) if response_times else None

        # Update proxy info
        proxy.last_tested = datetime.now().isoformat()
        proxy.response_time = avg_response_time

        if success_rate >= self.success_threshold:
            proxy.status = "Valid"
            self.stats['valid_proxies'] += 1
        else:
            proxy.status = "Invalid"
            self.stats['invalid_proxies'] += 1

        self.stats['total_tested'] += 1
        return proxy

    def _test_proxy_url(self, proxy: ProxyInfo, test_url: str) -> Dict[str, Any]:
        """Test proxy with a specific URL."""
        proxy_url = f"http://{proxy.ip}:{proxy.port}"
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        start_time = time.time()

        try:
            # Suppress SSL warnings for this request
            with requests.Session() as session:
                session.verify = False
                response = session.get(
                    test_url,
                    proxies=proxies,
                    timeout=self.timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )

            response_time = time.time() - start_time

            return {
                'success': response.status_code == 200,
                'response_time': response_time,
                'status_code': response.status_code
            }

        except Exception:
            return {
                'success': False,
                'response_time': time.time() - start_time,
                'error': 'connection_error'
            }

    def validate_proxies(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """Validate multiple proxies concurrently with progress indication."""
        if not proxies:
            return []

        total_proxies = len(proxies)
        self.logger.info(f"Starting validation of {total_proxies} proxies")
        validated_proxies = []
        completed_count = 0

        # Progress tracking
        def update_progress():
            nonlocal completed_count
            completed_count += 1
            percentage = (completed_count / total_proxies) * 100

            # Create progress bar
            bar_length = 40
            filled_length = int(bar_length * completed_count / total_proxies)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)

            # Print progress (overwrite previous line)
            print(f"\rValidating: [{bar}] {percentage:.1f}% ({completed_count}/{total_proxies})", end='', flush=True)

        # Use ThreadPoolExecutor for concurrent validation
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.concurrent_tests) as executor:
            future_to_proxy = {
                executor.submit(self.validate_proxy, proxy): proxy
                for proxy in proxies
            }

            for future in concurrent.futures.as_completed(future_to_proxy):
                try:
                    validated_proxy = future.result()
                    validated_proxies.append(validated_proxy)
                except Exception as e:
                    original_proxy = future_to_proxy[future]
                    self.logger.debug(f"Validation failed for {original_proxy}: {e}")
                    original_proxy.status = "Invalid"
                    original_proxy.last_tested = datetime.now().isoformat()
                    validated_proxies.append(original_proxy)

                # Update progress
                update_progress()

        # Complete progress bar and move to next line
        print()  # New line after progress bar

        valid_count = sum(1 for p in validated_proxies if p.status == "Valid")
        self.logger.info(f"Validation complete: {valid_count}/{len(proxies)} proxies are valid")

        return validated_proxies


# ============================================================================
# PROXY COLLECTOR
# ============================================================================

class ProxyCollector:
    """Main proxy collection coordinator."""

    def __init__(self, country: str = "RU"):
        """Initialize the proxy collector."""
        self.country = country.upper()
        self.config = EMBEDDED_CONFIG
        self.logger = logging.getLogger('collector')
        self.validator = ProxyValidator(self.config)

        # Initialize scrapers for the specified country
        self.scrapers = self._initialize_scrapers()

    def _initialize_scrapers(self) -> Dict[str, BaseScraper]:
        """Initialize scrapers for the specified country."""
        scrapers = {}
        scraper_classes = {
            'geonode': GeoNodeScraper,
            'proxynova': ProxyNovaScraper,
            'free_proxy_list': FreeProxyListScraper
        }

        country_sources = self.config['proxy_sources'].get(self.country, {})

        for source_name, source_config in country_sources.items():
            if source_name in scraper_classes and source_config.get('enabled', True):
                try:
                    scraper_class = scraper_classes[source_name]
                    scraper = scraper_class(source_name, source_config, self.country)
                    scrapers[source_name] = scraper
                    self.logger.debug(f"Initialized scraper: {source_name}")
                except Exception as e:
                    self.logger.error(f"Failed to initialize scraper {source_name}: {e}")

        return scrapers

    def collect_proxies(self, sources: List[str] = None) -> List[ProxyInfo]:
        """Collect proxies from specified sources."""
        self.logger.info(f"Starting {self.country} proxy collection")
        all_proxies = []

        # Determine which sources to use
        if sources:
            enabled_scrapers = {name: scraper for name, scraper in self.scrapers.items() if name in sources}
        else:
            enabled_scrapers = self.scrapers

        if not enabled_scrapers:
            self.logger.warning("No enabled scrapers found")
            return all_proxies

        # Collect from each source
        for source_name, scraper in enabled_scrapers.items():
            try:
                self.logger.info(f"Collecting proxies from {source_name}")
                source_proxies = scraper.scrape_proxies()

                if source_proxies:
                    all_proxies.extend(source_proxies)
                    self.logger.info(f"Collected {len(source_proxies)} proxies from {source_name}")
                else:
                    self.logger.warning(f"No proxies collected from {source_name}")

            except Exception as e:
                self.logger.error(f"Failed to collect from {source_name}: {e}")
                continue

        # Remove duplicates
        unique_proxies = self._remove_duplicates(all_proxies)
        self.logger.info(f"Collected {len(unique_proxies)} unique {self.country} proxies")
        return unique_proxies

    def _remove_duplicates(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """Remove duplicate proxies based on IP and port."""
        seen = set()
        unique_proxies = []

        for proxy in proxies:
            proxy_key = f"{proxy.ip}:{proxy.port}"
            if proxy_key not in seen:
                seen.add(proxy_key)
                unique_proxies.append(proxy)

        return unique_proxies

    def validate_proxies(self, proxies: List[ProxyInfo]) -> List[ProxyInfo]:
        """Validate collected proxies."""
        return self.validator.validate_proxies(proxies)

    def save_to_csv(self, proxies: List[ProxyInfo], filename: str) -> None:
        """Save proxies to CSV file."""
        if not proxies:
            self.logger.warning("No proxies to save")
            return

        columns = ['ip', 'port', 'type', 'country', 'anonymity', 'response_time',
                  'last_tested', 'status', 'source', 'uptime', 'speed']

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=columns)
                writer.writeheader()

                for proxy in proxies:
                    row = {}
                    for column in columns:
                        value = getattr(proxy, column, None)
                        row[column] = value if value is not None else ""
                    writer.writerow(row)

            self.logger.info(f"Saved {len(proxies)} proxies to {filename}")

        except Exception as e:
            self.logger.error(f"Failed to save proxies to CSV: {e}")
            raise

    def load_from_csv(self, filename: str) -> List[ProxyInfo]:
        """Load proxies from CSV file."""
        proxies = []

        try:
            with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                for row in reader:
                    try:
                        ip = row.get('ip', '').strip()
                        port_str = row.get('port', '').strip()

                        if not ip or not port_str:
                            continue

                        port = int(port_str)

                        proxy = ProxyInfo(
                            ip=ip,
                            port=port,
                            type=row.get('type', 'HTTP').strip() or 'HTTP',
                            country=row.get('country', 'Unknown').strip() or 'Unknown',
                            anonymity=row.get('anonymity', 'Unknown').strip() or 'Unknown',
                            response_time=float(row.get('response_time')) if row.get('response_time', '').strip() else None,
                            last_tested=row.get('last_tested', '').strip() or None,
                            status=row.get('status', 'Unknown').strip() or 'Unknown',
                            source=row.get('source', '').strip(),
                            uptime=float(row.get('uptime')) if row.get('uptime', '').strip() else None,
                            speed=int(row.get('speed')) if row.get('speed', '').strip() else None
                        )

                        proxies.append(proxy)

                    except Exception as e:
                        self.logger.warning(f"Skipping invalid proxy row: {e}")
                        continue

            self.logger.info(f"Loaded {len(proxies)} proxies from {filename}")

        except Exception as e:
            self.logger.error(f"Failed to load proxies from CSV: {e}")
            raise

        return proxies


# ============================================================================
# PROXY SERVER COMPONENTS
# ============================================================================

class ProxyPoolManager:
    """Manages the pool of upstream proxies."""

    def __init__(self, proxies: List[ProxyInfo]):
        """Initialize the proxy pool manager."""
        self.proxies = proxies
        self.healthy_proxies = [p for p in proxies if p.status == "Valid"]
        self.current_index = 0
        self.lock = threading.RLock()
        self.logger = logging.getLogger('pool_manager')

        self.logger.info(f"Initialized proxy pool with {len(self.healthy_proxies)} healthy proxies")

    def get_proxy(self) -> Optional[ProxyInfo]:
        """Get next proxy using round-robin selection."""
        with self.lock:
            if not self.healthy_proxies:
                return None

            proxy = self.healthy_proxies[self.current_index % len(self.healthy_proxies)]
            self.current_index = (self.current_index + 1) % len(self.healthy_proxies)
            return proxy

    def record_proxy_success(self, proxy: ProxyInfo) -> None:
        """Record successful proxy usage."""
        proxy.record_success()

    def record_proxy_failure(self, proxy: ProxyInfo) -> None:
        """Record proxy failure."""
        proxy.record_failure()

        # Remove from healthy list if too many failures
        if proxy.failures >= 3:
            with self.lock:
                if proxy in self.healthy_proxies:
                    self.healthy_proxies.remove(proxy)
                    self.logger.warning(f"Removed failed proxy: {proxy}")


class HTTPProxyHandler:
    """Handles HTTP proxy connections."""

    def __init__(self, pool_manager: ProxyPoolManager, auth_config: Dict[str, Any]):
        """Initialize HTTP proxy handler."""
        self.pool_manager = pool_manager
        self.auth_config = auth_config
        self.logger = logging.getLogger('http_handler')
        self.buffer_size = EMBEDDED_CONFIG['server']['buffer_size']

    def handle_connection(self, client_socket: socket.socket, client_address: Tuple[str, int]) -> None:
        """Handle incoming HTTP proxy connection."""
        try:
            client_socket.settimeout(30)

            # Read HTTP request
            request_data = self._read_http_request(client_socket)
            if not request_data:
                return

            # Parse request
            request_lines = request_data.decode('utf-8', errors='ignore').split('\r\n')
            if not request_lines:
                return

            request_line = request_lines[0]
            headers = self._parse_headers(request_lines[1:])

            # Check authentication
            if not self._authenticate_request(client_socket, headers):
                return

            # Parse request method and URL
            try:
                method, url, version = request_line.split(' ', 2)
            except ValueError:
                self._send_error_response(client_socket, 400, "Bad Request")
                return

            self.logger.debug(f"HTTP request: {method} {url} from {client_address[0]}")

            # Handle CONNECT method for HTTPS
            if method == 'CONNECT':
                self._handle_connect_request(client_socket, url)
            else:
                self._handle_http_request(client_socket, method, url, headers)

        except Exception as e:
            self.logger.error(f"Error handling HTTP connection: {e}")
        finally:
            self._close_socket_safely(client_socket)

    def _read_http_request(self, client_socket: socket.socket) -> Optional[bytes]:
        """Read HTTP request from client socket."""
        try:
            request_data = b''
            while b'\r\n\r\n' not in request_data:
                chunk = client_socket.recv(self.buffer_size)
                if not chunk:
                    break
                request_data += chunk
                if len(request_data) > 10485760:  # 10MB limit
                    return None
            return request_data
        except:
            return None

    def _parse_headers(self, header_lines: list) -> Dict[str, str]:
        """Parse HTTP headers."""
        headers = {}
        for line in header_lines:
            if ':' in line:
                key, value = line.split(':', 1)
                headers[key.strip().lower()] = value.strip()
        return headers

    def _authenticate_request(self, client_socket: socket.socket, headers: Dict[str, str]) -> bool:
        """Authenticate HTTP request."""
        if not self.auth_config.get('enabled', True):
            return True

        auth_header = headers.get('proxy-authorization', '')

        if auth_header.startswith('Basic '):
            try:
                encoded_creds = auth_header[6:]
                decoded_creds = base64.b64decode(encoded_creds).decode('utf-8')
                username, password = decoded_creds.split(':', 1)

                if (username == self.auth_config.get('username', 'user') and
                    password == self.auth_config.get('password', 'pass')):
                    return True
            except:
                pass

        # Send 407 Proxy Authentication Required
        response = (
            "HTTP/1.1 407 Proxy Authentication Required\r\n"
            f"Proxy-Authenticate: Basic realm=\"{self.auth_config.get('realm', 'Proxy')}\"\r\n"
            "Content-Length: 0\r\n"
            "\r\n"
        )

        try:
            client_socket.send(response.encode())
        except:
            pass

        return False

    def _handle_connect_request(self, client_socket: socket.socket, target: str) -> None:
        """Handle HTTP CONNECT request for HTTPS tunneling."""
        try:
            if ':' in target:
                host, port_str = target.rsplit(':', 1)
                port = int(port_str)
            else:
                host = target
                port = 443

            # Get upstream proxy
            upstream_proxy = self.pool_manager.get_proxy()
            if not upstream_proxy:
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return

            # Connect through upstream proxy
            upstream_socket = self._connect_through_upstream(upstream_proxy, host, port)
            if not upstream_socket:
                self.pool_manager.record_proxy_failure(upstream_proxy)
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return

            # Send 200 Connection Established
            response = "HTTP/1.1 200 Connection established\r\n\r\n"
            client_socket.send(response.encode())

            self.pool_manager.record_proxy_success(upstream_proxy)

            # Start tunneling
            self._tunnel_data(client_socket, upstream_socket)

        except Exception as e:
            self.logger.error(f"Error handling CONNECT request: {e}")
            self._send_error_response(client_socket, 500, "Internal Server Error")

    def _handle_http_request(self, client_socket: socket.socket, method: str, url: str, headers: Dict[str, str]) -> None:
        """Handle regular HTTP request."""
        try:
            # Get upstream proxy
            upstream_proxy = self.pool_manager.get_proxy()
            if not upstream_proxy:
                self._send_error_response(client_socket, 502, "Bad Gateway")
                return

            # Connect to upstream proxy
            upstream_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            upstream_socket.settimeout(15)
            upstream_socket.connect((upstream_proxy.ip, upstream_proxy.port))

            # Reconstruct HTTP request
            request_lines = [f"{method} {url} HTTP/1.1"]
            for key, value in headers.items():
                if key not in ['proxy-authorization', 'proxy-connection']:
                    request_lines.append(f"{key.title()}: {value}")

            request = '\r\n'.join(request_lines) + '\r\n\r\n'
            upstream_socket.send(request.encode())

            self.pool_manager.record_proxy_success(upstream_proxy)

            # Forward response back to client
            self._tunnel_data(client_socket, upstream_socket)

        except Exception as e:
            self.logger.error(f"Error handling HTTP request: {e}")
            if upstream_proxy:
                self.pool_manager.record_proxy_failure(upstream_proxy)

    def _connect_through_upstream(self, upstream_proxy: ProxyInfo, target_host: str, target_port: int) -> Optional[socket.socket]:
        """Connect to target through upstream proxy."""
        try:
            upstream_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            upstream_socket.settimeout(15)
            upstream_socket.connect((upstream_proxy.ip, upstream_proxy.port))

            # Use HTTP CONNECT method
            connect_request = (
                f"CONNECT {target_host}:{target_port} HTTP/1.1\r\n"
                f"Host: {target_host}:{target_port}\r\n"
                "\r\n"
            )

            upstream_socket.send(connect_request.encode())

            # Read response
            response = upstream_socket.recv(self.buffer_size).decode()
            if "200" not in response.split('\r\n')[0]:
                upstream_socket.close()
                return None

            return upstream_socket

        except Exception:
            return None

    def _tunnel_data(self, client_socket: socket.socket, upstream_socket: socket.socket) -> None:
        """Tunnel data between client and upstream sockets."""
        def forward_data(source: socket.socket, destination: socket.socket):
            try:
                while True:
                    data = source.recv(self.buffer_size)
                    if not data:
                        break
                    destination.send(data)
            except:
                pass
            finally:
                self._close_socket_safely(source)
                self._close_socket_safely(destination)

        # Start forwarding threads
        client_to_upstream = threading.Thread(
            target=forward_data,
            args=(client_socket, upstream_socket),
            daemon=True
        )

        upstream_to_client = threading.Thread(
            target=forward_data,
            args=(upstream_socket, client_socket),
            daemon=True
        )

        client_to_upstream.start()
        upstream_to_client.start()

        # Wait for both threads to complete
        client_to_upstream.join()
        upstream_to_client.join()

    def _send_error_response(self, client_socket: socket.socket, status_code: int, status_text: str) -> None:
        """Send HTTP error response."""
        response = (
            f"HTTP/1.1 {status_code} {status_text}\r\n"
            "Content-Length: 0\r\n"
            "Connection: close\r\n"
            "\r\n"
        )

        try:
            client_socket.send(response.encode())
        except:
            pass

    def _close_socket_safely(self, sock: socket.socket) -> None:
        """Safely close a socket."""
        try:
            sock.shutdown(socket.SHUT_RDWR)
        except:
            pass
        try:
            sock.close()
        except:
            pass


class ProxyServer:
    """Main proxy server class."""

    def __init__(self, proxies: List[ProxyInfo]):
        """Initialize the proxy server."""
        self.config = EMBEDDED_CONFIG
        self.server_config = self.config['server']
        self.auth_config = self.config['authentication']
        self.logger = logging.getLogger('proxy_server')

        # Initialize components
        self.pool_manager = ProxyPoolManager(proxies)
        self.http_handler = HTTPProxyHandler(self.pool_manager, self.auth_config)

        # Server settings
        self.bind_address = self.server_config.get('bind_address', '127.0.0.1')
        self.http_port = self.server_config.get('http_port', 8080)
        self.max_connections = self.server_config.get('max_connections', 100)

        # Server socket
        self.http_socket: Optional[socket.socket] = None
        self.http_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        self.running = False

        # Statistics
        self.stats = {
            'start_time': time.time(),
            'total_connections': 0,
            'active_connections': 0
        }

    def start(self) -> None:
        """Start the proxy server."""
        self.logger.info("Starting Unified Proxy Server")

        try:
            # Check if we have healthy proxies
            if not self.pool_manager.healthy_proxies:
                self.logger.warning("No healthy upstream proxies available!")
            else:
                self.logger.info(f"Loaded {len(self.pool_manager.healthy_proxies)} healthy upstream proxies")

            # Start HTTP proxy server
            self._start_http_server()

            self.running = True
            self.logger.info(f"Proxy server started successfully")
            self.logger.info(f"HTTP proxy listening on {self.bind_address}:{self.http_port}")

            if self.auth_config.get('enabled', True):
                username = self.auth_config.get('username', 'user')
                password = self.auth_config.get('password', 'pass')
                self.logger.info(f"Authentication enabled (user: {username}, pass: {password})")
            else:
                self.logger.warning("Authentication disabled")

        except Exception as e:
            self.logger.error(f"Failed to start proxy server: {e}")
            self.shutdown()
            raise

    def _start_http_server(self) -> None:
        """Start HTTP proxy server."""
        self.http_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.http_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.http_socket.bind((self.bind_address, self.http_port))
        self.http_socket.listen(self.max_connections)

        self.http_thread = threading.Thread(
            target=self._http_server_worker,
            daemon=True,
            name="HTTPProxyServer"
        )
        self.http_thread.start()

    def _http_server_worker(self) -> None:
        """HTTP proxy server worker thread."""
        self.logger.info(f"HTTP proxy server started on {self.bind_address}:{self.http_port}")

        while not self.shutdown_event.is_set():
            try:
                self.http_socket.settimeout(1.0)
                client_socket, client_address = self.http_socket.accept()

                # Update statistics
                self.stats['total_connections'] += 1
                self.stats['active_connections'] += 1

                # Handle connection in separate thread
                connection_thread = threading.Thread(
                    target=self._handle_http_connection,
                    args=(client_socket, client_address),
                    daemon=True
                )
                connection_thread.start()

            except socket.timeout:
                continue
            except Exception as e:
                if not self.shutdown_event.is_set():
                    self.logger.error(f"HTTP server error: {e}")
                break

    def _handle_http_connection(self, client_socket: socket.socket, client_address: tuple) -> None:
        """Handle HTTP proxy connection."""
        try:
            self.http_handler.handle_connection(client_socket, client_address)
        except Exception as e:
            self.logger.debug(f"HTTP connection error from {client_address[0]}: {e}")
        finally:
            self.stats['active_connections'] -= 1

    def get_statistics(self) -> dict:
        """Get server statistics."""
        stats = self.stats.copy()
        stats['uptime'] = time.time() - stats['start_time']
        stats['healthy_proxies'] = len(self.pool_manager.healthy_proxies)
        return stats

    def shutdown(self) -> None:
        """Shutdown the proxy server."""
        if not self.running:
            return

        self.logger.info("Shutting down proxy server")
        self.shutdown_event.set()
        self.running = False

        # Close server socket
        if self.http_socket:
            try:
                self.http_socket.close()
            except:
                pass

        # Wait for thread to finish
        if self.http_thread and self.http_thread.is_alive():
            self.http_thread.join(timeout=5)

        self.logger.info("Proxy server shutdown complete")

    def wait_for_shutdown(self) -> None:
        """Wait for shutdown signal."""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
            self.shutdown()


# ============================================================================
# TEST CLIENT
# ============================================================================

class TestClient:
    """Built-in test client for proxy validation."""

    def __init__(self, proxy_host: str = "127.0.0.1", proxy_port: int = 8080,
                 username: str = "proxyuser", password: str = "proxypass"):
        """Initialize test client."""
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.username = username
        self.password = password
        self.logger = logging.getLogger('test_client')

    def test_proxy_connection(self) -> bool:
        """Test proxy connection and verify IP change."""
        self.logger.info("Testing proxy connection...")

        try:
            # Get original IP
            original_ip = self._get_external_ip_direct()
            if not original_ip:
                self.logger.error("Failed to get original IP address")
                return False

            self.logger.info(f"Original IP: {original_ip}")

            # Test through proxy
            proxy_ip = self._get_external_ip_through_proxy()
            if not proxy_ip:
                self.logger.error("Failed to get IP through proxy")
                return False

            self.logger.info(f"Proxy IP: {proxy_ip}")

            # Verify IP change
            if original_ip != proxy_ip:
                self.logger.info("✅ Proxy test successful - IP address changed!")
                return True
            else:
                self.logger.warning("⚠ Proxy test inconclusive - IP address unchanged")
                return False

        except Exception as e:
            self.logger.error(f"Proxy test failed: {e}")
            return False

    def _get_external_ip_direct(self) -> Optional[str]:
        """Get external IP address directly."""
        try:
            with requests.Session() as session:
                session.verify = False
                response = session.get('http://httpbin.org/ip', timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    return data.get('origin', '').split(',')[0].strip()
        except:
            pass

        # Fallback
        try:
            with requests.Session() as session:
                session.verify = False
                response = session.get('http://icanhazip.com', timeout=10)
                if response.status_code == 200:
                    return response.text.strip()
        except:
            pass

        return None

    def _get_external_ip_through_proxy(self) -> Optional[str]:
        """Get external IP address through proxy."""
        try:
            proxy_url = f"http://{self.username}:{self.password}@{self.proxy_host}:{self.proxy_port}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            with requests.Session() as session:
                session.verify = False
                response = session.get('http://httpbin.org/ip', proxies=proxies, timeout=15)
                if response.status_code == 200:
                    data = response.json()
                    return data.get('origin', '').split(',')[0].strip()

        except Exception as e:
            self.logger.debug(f"Proxy request failed: {e}")

        return None


# ============================================================================
# MAIN APPLICATION
# ============================================================================

class UnifiedProxySystem:
    """Main application class that coordinates all components."""

    def __init__(self):
        """Initialize the unified proxy system."""
        self.logger = logging.getLogger('unified_system')
        self.collector: Optional[ProxyCollector] = None
        self.server: Optional[ProxyServer] = None
        self.test_client: Optional[TestClient] = None

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info("Received shutdown signal")
        self.shutdown()
        sys.exit(0)

    def collect_proxies(self, country: str, sources: List[str] = None, validate: bool = False) -> List[ProxyInfo]:
        """Collect proxies from specified country and sources."""
        self.collector = ProxyCollector(country)

        self.logger.info(f"Starting {country} proxy collection")
        proxies = self.collector.collect_proxies(sources)

        if not proxies:
            self.logger.error("No proxies collected")
            return []

        if validate:
            self.logger.info("Validating collected proxies...")
            proxies = self.collector.validate_proxies(proxies)
            valid_proxies = [p for p in proxies if p.status == "Valid"]
            self.logger.info(f"Validation complete: {len(valid_proxies)}/{len(proxies)} proxies are valid")
            return valid_proxies

        return proxies

    def start_server(self, proxies: List[ProxyInfo]) -> None:
        """Start the proxy server with given proxies."""
        if not proxies:
            self.logger.error("Cannot start server: no proxies provided")
            return

        self.server = ProxyServer(proxies)
        self.server.start()

        # Initialize test client
        auth_config = EMBEDDED_CONFIG['authentication']
        server_config = EMBEDDED_CONFIG['server']

        self.test_client = TestClient(
            proxy_host=server_config.get('bind_address', '127.0.0.1'),
            proxy_port=server_config.get('http_port', 8080),
            username=auth_config.get('username', 'proxyuser'),
            password=auth_config.get('password', 'proxypass')
        )

    def test_proxy_functionality(self) -> bool:
        """Test proxy functionality using built-in test client."""
        if not self.test_client:
            self.logger.error("Test client not initialized")
            return False

        # Wait a moment for server to be ready
        time.sleep(2)

        return self.test_client.test_proxy_connection()

    def save_proxies(self, proxies: List[ProxyInfo], filename: str) -> None:
        """Save proxies to CSV file."""
        if not self.collector:
            self.collector = ProxyCollector()

        self.collector.save_to_csv(proxies, filename)

    def load_proxies(self, filename: str) -> List[ProxyInfo]:
        """Load proxies from CSV file."""
        if not self.collector:
            self.collector = ProxyCollector()

        return self.collector.load_from_csv(filename)

    def shutdown(self) -> None:
        """Shutdown all components."""
        self.logger.info("Shutting down unified proxy system")

        if self.server:
            self.server.shutdown()

        self.logger.info("Shutdown complete")

    def run_collection_only(self, country: str, sources: List[str] = None,
                          validate: bool = False, output_file: str = None) -> int:
        """Run collection-only mode."""
        try:
            proxies = self.collect_proxies(country, sources, validate)

            if not proxies:
                self.logger.error("No proxies collected")
                return 1

            if output_file:
                self.save_proxies(proxies, output_file)

            self.logger.info(f"Collection complete: {len(proxies)} proxies")
            return 0

        except Exception as e:
            self.logger.error(f"Collection failed: {e}")
            return 1

    def run_server_only(self, proxy_file: str) -> int:
        """Run server-only mode."""
        try:
            proxies = self.load_proxies(proxy_file)

            if not proxies:
                self.logger.error("No proxies loaded from file")
                return 1

            # Filter for valid proxies
            valid_proxies = [p for p in proxies if p.status == "Valid"]
            if not valid_proxies:
                self.logger.warning("No valid proxies found, using all proxies")
                valid_proxies = proxies

            self.start_server(valid_proxies)
            self.server.wait_for_shutdown()

            return 0

        except Exception as e:
            self.logger.error(f"Server failed: {e}")
            return 1

    def run_integrated_mode(self, country: str, sources: List[str] = None,
                          test: bool = False) -> int:
        """Run integrated collection + server mode."""
        try:
            # Collect and validate proxies
            proxies = self.collect_proxies(country, sources, validate=True)

            if not proxies:
                self.logger.error("No valid proxies collected")
                return 1

            # Start server
            self.start_server(proxies)

            # Run test if requested
            if test:
                self.logger.info("Running proxy functionality test...")
                test_result = self.test_proxy_functionality()

                if test_result:
                    self.logger.info("✅ Integrated test completed successfully!")
                else:
                    self.logger.warning("⚠ Integrated test failed or inconclusive")

                # Shutdown after test
                self.shutdown()
                return 0 if test_result else 1
            else:
                # Keep server running
                self.server.wait_for_shutdown()
                return 0

        except Exception as e:
            self.logger.error(f"Integrated mode failed: {e}")
            return 1


def setup_logging(verbose: bool = False) -> None:
    """Setup logging configuration."""
    log_config = EMBEDDED_CONFIG['logging']

    level = logging.DEBUG if verbose else getattr(logging, log_config.get('level', 'INFO'))
    format_str = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[logging.StreamHandler(sys.stdout)]
    )

    # Reduce noise from requests library
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def main() -> int:
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Unified Proxy System - Collect and serve proxies from Russia or China",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Collect Chinese proxies and start server with test
  python unified_proxy_system.py --country CN --collect --serve --test

  # Collection only
  python unified_proxy_system.py --country RU --collect-only --output proxies.csv

  # Server only (using existing proxy file)
  python unified_proxy_system.py --serve --proxy-file existing_proxies.csv

  # Collect and validate without server
  python unified_proxy_system.py --country CN --collect --validate --output cn_proxies.csv
        """
    )

    # Country selection
    parser.add_argument(
        '--country',
        choices=['RU', 'CN'],
        default='RU',
        help='Country to collect proxies from (default: RU)'
    )

    # Operation modes
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument(
        '--collect-only',
        action='store_true',
        help='Collection only mode'
    )
    mode_group.add_argument(
        '--serve',
        action='store_true',
        help='Server only mode (requires --proxy-file) or integrated mode (with --collect)'
    )

    # Collection options
    parser.add_argument(
        '--collect',
        action='store_true',
        help='Collect proxies (used with --serve for integrated mode)'
    )
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Validate collected proxies'
    )
    parser.add_argument(
        '--sources',
        type=str,
        help='Comma-separated list of sources (geonode,proxynova,free_proxy_list)'
    )

    # File options
    parser.add_argument(
        '--output',
        type=str,
        help='Output CSV file for collected proxies'
    )
    parser.add_argument(
        '--proxy-file',
        type=str,
        help='Input CSV file with existing proxies (for server-only mode)'
    )

    # Test options
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run built-in proxy test (with integrated mode)'
    )

    # Logging
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.verbose)

    # Validate arguments
    if args.serve and not args.collect and not args.proxy_file:
        print("Error: --serve requires either --collect or --proxy-file")
        return 1

    if args.test and not (args.serve and args.collect):
        print("Error: --test requires integrated mode (--collect --serve)")
        return 1

    # Parse sources
    sources = None
    if args.sources:
        sources = [s.strip() for s in args.sources.split(',')]

    try:
        # Create and run unified system
        system = UnifiedProxySystem()

        if args.collect_only:
            return system.run_collection_only(
                country=args.country,
                sources=sources,
                validate=args.validate,
                output_file=args.output
            )

        elif args.serve and args.collect:
            # Integrated mode
            return system.run_integrated_mode(
                country=args.country,
                sources=sources,
                test=args.test
            )

        elif args.serve and args.proxy_file:
            # Server-only mode
            return system.run_server_only(args.proxy_file)

        else:
            print("Error: Invalid argument combination")
            return 1

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 0
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
