"""
Configuration Manager for Russian Proxy Collector
Handles loading and managing configuration settings from JSON files.
"""

import json
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path


class ConfigManager:
    """Manages configuration settings for the proxy collector."""
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from the JSON file."""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.logger.info(f"Configuration loaded from {self.config_path}")
            self._validate_config()
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def _validate_config(self) -> None:
        """Validate the loaded configuration."""
        required_sections = ['proxy_sources', 'validation', 'output', 'logging', 'general']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate proxy sources
        if not self.config['proxy_sources']:
            raise ValueError("No proxy sources configured")
        
        for source_name, source_config in self.config['proxy_sources'].items():
            required_fields = ['name', 'url', 'method', 'enabled']
            for field in required_fields:
                if field not in source_config:
                    raise ValueError(f"Missing required field '{field}' in source '{source_name}'")
    
    def get_proxy_sources(self) -> Dict[str, Dict[str, Any]]:
        """Get all configured proxy sources."""
        return self.config.get('proxy_sources', {})
    
    def get_enabled_sources(self) -> Dict[str, Dict[str, Any]]:
        """Get only enabled proxy sources."""
        return {
            name: config for name, config in self.get_proxy_sources().items()
            if config.get('enabled', False)
        }
    
    def get_source_config(self, source_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific proxy source."""
        return self.config.get('proxy_sources', {}).get(source_name)
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration."""
        return self.config.get('validation', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration."""
        return self.config.get('output', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get('logging', {})
    
    def get_general_config(self) -> Dict[str, Any]:
        """Get general configuration."""
        return self.config.get('general', {})
    
    def get_test_urls(self) -> List[str]:
        """Get list of test URLs for proxy validation."""
        return self.get_validation_config().get('test_urls', [])
    
    def get_csv_file_path(self) -> str:
        """Get the CSV output file path."""
        return self.get_output_config().get('csv_file', 'russian_proxies.csv')
    
    def get_csv_columns(self) -> List[str]:
        """Get the CSV column names."""
        return self.get_output_config().get('csv_columns', [])
    
    def get_max_workers(self) -> int:
        """Get the maximum number of worker threads."""
        return self.get_general_config().get('max_workers', 10)
    
    def get_user_agents(self) -> List[str]:
        """Get list of user agents for requests."""
        return self.get_general_config().get('user_agents', [])
    
    def update_source_status(self, source_name: str, enabled: bool) -> None:
        """Update the enabled status of a proxy source."""
        if source_name in self.config.get('proxy_sources', {}):
            self.config['proxy_sources'][source_name]['enabled'] = enabled
            self.save_config()
    
    def save_config(self) -> None:
        """Save the current configuration to file."""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def get_config_value(self, *keys: str, default: Any = None) -> Any:
        """
        Get a nested configuration value using dot notation.
        
        Args:
            *keys: Keys to traverse the configuration dictionary
            default: Default value if key path doesn't exist
            
        Returns:
            The configuration value or default
        """
        current = self.config
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    
    def __str__(self) -> str:
        """String representation of the configuration."""
        enabled_sources = list(self.get_enabled_sources().keys())
        return f"ConfigManager(sources={len(enabled_sources)}, enabled={enabled_sources})"
