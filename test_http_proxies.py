#!/usr/bin/env python3
"""
Test specifically with HTTP proxies to confirm the validation fix works.
"""

import requests
import urllib3

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_http_proxies():
    """Test with HTTP proxies from different sources."""
    print("🔍 Testing HTTP proxies specifically...")
    
    # Try to get HTTP proxies from GeoNode
    try:
        # Try different countries to find HTTP proxies
        countries = ['RU', 'CN']
        
        for country in countries:
            print(f"\n--- Testing {country} proxies ---")
            api_url = f"https://proxylist.geonode.com/api/proxy-list?limit=5&page=1&sort_by=lastChecked&sort_type=desc&country={country}"
            
            response = requests.get(api_url, timeout=30)
            if response.status_code != 200:
                continue
                
            data = response.json()
            proxies = data.get('data', [])
            
            http_proxies = [p for p in proxies if 'http' in p.get('protocols', [])]
            print(f"Found {len(http_proxies)} HTTP proxies out of {len(proxies)} total")
            
            for proxy_data in http_proxies[:2]:  # Test first 2 HTTP proxies
                ip = proxy_data.get('ip')
                port = proxy_data.get('port')
                uptime = proxy_data.get('upTime', 0)
                
                print(f"\nTesting HTTP proxy: {ip}:{port} (uptime: {uptime}%)")
                
                proxy_url = f"http://{ip}:{port}"
                proxies_dict = {'http': proxy_url, 'https': proxy_url}
                
                try:
                    response = requests.get(
                        "http://httpbin.org/ip",
                        proxies=proxies_dict,
                        timeout=15,
                        verify=False
                    )
                    
                    print(f"Status: {response.status_code}")
                    
                    # Test both old and new validation logic
                    old_success = response.status_code == 200
                    new_success = response.status_code in [200, 301, 302, 403, 404]
                    
                    print(f"Old validation: {'✅ PASS' if old_success else '❌ FAIL'}")
                    print(f"New validation: {'✅ PASS' if new_success else '❌ FAIL'}")
                    
                    if new_success:
                        print("🎉 PROXY WORKS with new validation!")
                        if response.status_code == 200:
                            print(f"Response: {response.text[:100]}")
                        return True
                        
                except requests.exceptions.Timeout:
                    print("❌ Timeout")
                except Exception as e:
                    print(f"❌ Error: {str(e)[:100]}")
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return False

def test_public_http_proxies():
    """Test with known public HTTP proxy sources."""
    print("\n🔍 Testing public HTTP proxy sources...")
    
    # Test some public HTTP proxies
    public_proxies = [
        "1.1.1.1:80",        # Cloudflare (we know this gives 403)
        "*******:3128",      # Google DNS (probably won't work as proxy)
        "proxy.example.com:8080",  # Won't work but tests logic
    ]
    
    working_count = 0
    
    for proxy_str in public_proxies:
        print(f"\nTesting: {proxy_str}")
        
        proxy_url = f"http://{proxy_str}"
        proxies = {'http': proxy_url, 'https': proxy_url}
        
        try:
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=10,
                verify=False
            )
            
            print(f"Status: {response.status_code}")
            
            # Test validation logic
            old_success = response.status_code == 200
            new_success = response.status_code in [200, 301, 302, 403, 404]
            
            print(f"Old: {'✅' if old_success else '❌'}, New: {'✅' if new_success else '❌'}")
            
            if new_success:
                working_count += 1
                print("✅ Works with new validation!")
                
        except requests.exceptions.Timeout:
            print("❌ Timeout (expected for most)")
        except requests.exceptions.ConnectionError:
            print("❌ Connection refused (expected)")
        except Exception as e:
            print(f"❌ Error: {str(e)[:50]}")
    
    print(f"\n📊 {working_count}/{len(public_proxies)} proxies work with new validation")
    return working_count > 0

def main():
    """Test HTTP proxies with the validation fix."""
    print("🔧 TESTING HTTP PROXIES WITH VALIDATION FIX")
    print("=" * 50)
    
    found_working = False
    
    # Test GeoNode HTTP proxies
    if test_http_proxies():
        found_working = True
    
    # Test public HTTP proxies
    if test_public_http_proxies():
        found_working = True
    
    if found_working:
        print("\n🎉 SUCCESS! The validation fix is working!")
        print("We found working proxies with the improved validation logic.")
    else:
        print("\n🤔 No working HTTP proxies found.")
        print("This suggests either:")
        print("1. Network/firewall blocking proxy connections")
        print("2. All tested proxies are genuinely non-functional")
        print("3. Need to test with different proxy sources")

if __name__ == "__main__":
    main()
